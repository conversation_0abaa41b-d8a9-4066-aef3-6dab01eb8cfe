// Vercel Edge Function - 全球网络延迟测试
// 部署到全球边缘节点，提供真实的网络延迟测试

export const config = {
  runtime: 'edge',
  regions: [
    'hkg1', // 香港
    'sin1', // 新加坡  
    'nrt1', // 东京
    'icn1', // 首尔
    'sfo1', // 旧金山
    'iad1', // 华盛顿
    'fra1', // 法兰克福
    'lhr1', // 伦敦
    'syd1', // 悉尼
    'bom1'  // 孟买
  ]
}

// 获取地理位置信息
function getLocationInfo(request) {
  const country = request.geo?.country || 'Unknown';
  const region = request.geo?.region || 'Unknown';
  const city = request.geo?.city || 'Unknown';
  const latitude = request.geo?.latitude || 0;
  const longitude = request.geo?.longitude || 0;
  
  return {
    country,
    region, 
    city,
    latitude,
    longitude,
    timezone: request.geo?.timezone || 'UTC'
  };
}

// 执行HTTP延迟测试
async function performHttpLatencyTest(target, timeout = 5000) {
  const startTime = Date.now();
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(target, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Vercel-Edge-Ping-Test/1.0'
      }
    });
    
    clearTimeout(timeoutId);
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    return {
      success: true,
      latency,
      status: response.status,
      statusText: response.statusText
    };
    
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    return {
      success: false,
      latency,
      error: error.name === 'AbortError' ? 'Timeout' : error.message
    };
  }
}

// 执行多次测试取平均值
async function performMultipleTests(target, testCount = 3) {
  const results = [];
  
  for (let i = 0; i < testCount; i++) {
    const result = await performHttpLatencyTest(target);
    results.push(result);
    
    // 测试间隔100ms
    if (i < testCount - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  // 计算统计数据
  const successfulTests = results.filter(r => r.success);
  const latencies = successfulTests.map(r => r.latency);
  
  if (latencies.length === 0) {
    return {
      success: false,
      error: 'All tests failed',
      results
    };
  }
  
  // 排序并计算统计值
  latencies.sort((a, b) => a - b);
  
  return {
    success: true,
    averageLatency: Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length),
    minLatency: latencies[0],
    maxLatency: latencies[latencies.length - 1],
    medianLatency: latencies[Math.floor(latencies.length / 2)],
    successRate: (successfulTests.length / results.length) * 100,
    testCount: results.length,
    results
  };
}

// 主处理函数
export default async function handler(request) {
  // 只允许POST请求
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({
      error: 'Method not allowed'
    }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    });
  }
  
  try {
    // 解析请求体
    const body = await request.json();
    const { target, testCount = 3 } = body;
    
    if (!target) {
      return new Response(JSON.stringify({
        error: 'Target URL is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
    
    // 获取地理位置信息
    const location = getLocationInfo(request);
    
    // 执行延迟测试
    const testResult = await performMultipleTests(target, Math.min(testCount, 5));
    
    // 构建响应
    const response = {
      success: testResult.success,
      target,
      location,
      timestamp: new Date().toISOString(),
      provider: 'vercel-edge',
      region: request.geo?.region || 'unknown',
      testMethod: 'Vercel-Edge-HTTP-HEAD',
      ...testResult
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
    
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Internal server error',
      message: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}
