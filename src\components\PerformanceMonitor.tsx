'use client';

import React, { useState, useEffect, useRef } from 'react';
import { historyStorage, PerformanceAlert, TrendData } from '../utils/HistoryStorage';

interface PerformanceMonitorProps {
  target: string;
  isDarkMode: boolean;
}

interface MonitoringStats {
  uptime: number;
  avgLatency: number;
  maxLatency: number;
  minLatency: number;
  totalTests: number;
  successRate: number;
  lastTestTime: number;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ target, isDarkMode }) => {
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [stats, setStats] = useState<MonitoringStats | null>(null);
  const [trendData, setTrendData] = useState<TrendData | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastReadTime, setLastReadTime] = useState(0);
  const [monitoringProgress, setMonitoringProgress] = useState(0);

  // 使用useRef存储interval，避免内存泄漏
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 加载监控数据
  const loadMonitoringData = () => {
    if (!target) return;

    // 获取告警
    const unreadAlerts = historyStorage.getUnreadAlerts(lastReadTime);
    const allAlerts = historyStorage.getAllAlerts()
      .filter(alert => alert.target === target)
      .slice(0, 10); // 最近10条告警
    setAlerts(allAlerts);

    // 获取趋势数据
    const trend = historyStorage.getTrendData(target, 24);
    setTrendData(trend);

    // 计算统计数据
    const records = historyStorage.getRecordsByTarget(target, 100);
    if (records.length > 0) {
      const successfulRecords = records.filter(r => r.status === 'success');
      const latencies = successfulRecords.map(r => r.latency);
      
      const stats: MonitoringStats = {
        uptime: (successfulRecords.length / records.length) * 100,
        avgLatency: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
        maxLatency: latencies.length > 0 ? Math.max(...latencies) : 0,
        minLatency: latencies.length > 0 ? Math.min(...latencies) : 0,
        totalTests: records.length,
        successRate: (successfulRecords.length / records.length) * 100,
        lastTestTime: records[0]?.timestamp || 0
      };
      
      setStats(stats);
    }
  };

  // 开始/停止监控
  const toggleMonitoring = () => {
    if (!isMonitoring) {
      // 开始监控时显示进度动画
      setMonitoringProgress(0);
      const progressInterval = setInterval(() => {
        setMonitoringProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 0; // 重置进度，形成循环动画
          }
          return prev + 2;
        });
      }, 100);

      // 保存interval ID以便停止时清除
      intervalRef.current = progressInterval;
    } else {
      // 停止监控时清除进度动画
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setMonitoringProgress(0);
    }

    setIsMonitoring(!isMonitoring);
  };

  // 清除告警
  const clearAlerts = () => {
    setLastReadTime(Date.now());
    setAlerts([]);
  };

  // 获取告警严重程度颜色
  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取告警图标
  const getAlertIcon = (type: string): string => {
    switch (type) {
      case 'latency_spike': return '⚡';
      case 'packet_loss': return '📉';
      case 'bandwidth_drop': return '🐌';
      case 'connection_failure': return '❌';
      default: return '⚠️';
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  // 格式化相对时间
  const formatRelativeTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  };

  // 获取趋势指示器
  const getTrendIndicator = (trend: string): { icon: string, color: string, text: string } => {
    switch (trend) {
      case 'improving':
        return { icon: '📈', color: 'text-green-600', text: '性能改善' };
      case 'degrading':
        return { icon: '📉', color: 'text-red-600', text: '性能下降' };
      default:
        return { icon: '➡️', color: 'text-gray-600', text: '性能稳定' };
    }
  };

  useEffect(() => {
    loadMonitoringData();

    // 定期刷新数据
    const interval = setInterval(loadMonitoringData, 30000); // 30秒刷新一次

    return () => {
      clearInterval(interval);
      // 清理监控进度interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [target, lastReadTime]);

  if (!target) {
    return (
      <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
        <p className={`text-center ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          请输入目标地址开始监控
        </p>
      </div>
    );
  }

  return (
    <div className={`performance-monitor p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-6">
        <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          📊 性能监控
        </h3>
        <div className="flex items-center space-x-3">
          <button
            onClick={clearAlerts}
            className="px-3 py-1 text-sm rounded-lg bg-gray-500 hover:bg-gray-600 text-white transition-colors"
          >
            清除告警
          </button>
          <button
            onClick={toggleMonitoring}
            className={`relative px-4 py-2 rounded-lg font-medium transition-colors duration-300 overflow-hidden ${
              isMonitoring
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {/* 监控进度条 */}
            {isMonitoring && (
              <div
                className="absolute inset-0 bg-red-500 opacity-30 transition-all duration-100"
                style={{ width: `${monitoringProgress}%` }}
              />
            )}

            {/* 按钮文本 */}
            <span className="relative z-10">
              {isMonitoring ? (
                <span className="flex items-center space-x-2">
                  <span className="animate-pulse">⏹️</span>
                  <span>停止监控</span>
                </span>
              ) : (
                '▶️ 开始监控'
              )}
            </span>
          </button>
        </div>
      </div>

      {/* 统计概览 */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <div className={`text-2xl font-bold ${stats.uptime >= 95 ? 'text-green-600' : stats.uptime >= 90 ? 'text-yellow-600' : 'text-red-600'}`}>
              {stats.uptime.toFixed(1)}%
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              可用性
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <div className={`text-2xl font-bold ${stats.avgLatency <= 100 ? 'text-green-600' : stats.avgLatency <= 300 ? 'text-yellow-600' : 'text-red-600'}`}>
              {Math.round(stats.avgLatency)}ms
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              平均延迟
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <div className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {stats.totalTests}
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              总测试次数
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <div className={`text-2xl font-bold ${stats.successRate >= 95 ? 'text-green-600' : stats.successRate >= 90 ? 'text-yellow-600' : 'text-red-600'}`}>
              {stats.successRate.toFixed(1)}%
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              成功率
            </div>
          </div>
        </div>
      )}

      {/* 趋势指示器 */}
      {trendData && (
        <div className={`p-4 rounded-lg mb-6 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-lg">📈</span>
              <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                24小时趋势
              </span>
            </div>
            <div className="flex items-center space-x-4">
              {(() => {
                const trend = getTrendIndicator(trendData.trend);
                return (
                  <div className="flex items-center space-x-1">
                    <span>{trend.icon}</span>
                    <span className={`text-sm font-medium ${trend.color}`}>
                      {trend.text}
                    </span>
                  </div>
                );
              })()}
              <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                可靠性: {trendData.reliability}%
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 告警列表 */}
      <div>
        <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          🚨 性能告警 {alerts.length > 0 && `(${alerts.length})`}
        </h4>
        
        {alerts.length === 0 ? (
          <div className={`p-4 rounded-lg text-center ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              🎉 暂无告警，网络状况良好
            </span>
          </div>
        ) : (
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {alerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border-l-4 ${
                  alert.severity === 'critical' ? 'border-red-500' :
                  alert.severity === 'high' ? 'border-orange-500' :
                  alert.severity === 'medium' ? 'border-yellow-500' :
                  'border-blue-500'
                } ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2">
                    <span className="text-lg">{getAlertIcon(alert.type)}</span>
                    <div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {alert.message}
                      </div>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className={`text-xs px-2 py-1 rounded ${getSeverityColor(alert.severity)}`}>
                          {alert.severity === 'critical' ? '严重' :
                           alert.severity === 'high' ? '高' :
                           alert.severity === 'medium' ? '中' : '低'}
                        </span>
                        <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {formatRelativeTime(alert.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {alert.value > 0 && `${alert.value}${alert.type.includes('latency') ? 'ms' : alert.type.includes('loss') ? '%' : 'Kbps'}`}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
