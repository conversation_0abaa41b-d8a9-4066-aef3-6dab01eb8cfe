#!/usr/bin/env node

// 测试Google访问准确性的脚本
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testGoogleAccuracy() {
  console.log('🔍 测试Google访问准确性');
  console.log('=' .repeat(50));
  
  console.log('📊 测试目标: https://www.google.com');
  console.log('🌍 预期结果: 在中国大陆应该无法访问或延迟极高');
  console.log('');

  try {
    console.log('🚀 调用修复后的API...');
    
    const response = await fetch('http://localhost:3003/api/ping-cloudping', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target: 'https://www.google.com'
      }),
      timeout: 30000
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API调用成功');
      console.log(`📊 测试类型: ${data.metadata?.testType}`);
      console.log(`🌐 数据源: ${data.metadata?.dataSource}`);
      console.log(`📍 总节点数量: ${data.results?.length || 0}`);
      
      if (data.results && data.results.length > 0) {
        console.log('\n📋 详细测试结果:');
        
        data.results.forEach((result, index) => {
          const status = result.status === 'success' ? '✅' : '❌';
          const ping = result.ping || result.latency || 'N/A';
          const city = result.location?.city || result.city || 'Unknown';
          const testMethod = result.testMethod || 'Unknown';
          
          console.log(`${index + 1}. ${status} ${city}: ${ping}ms (${testMethod})`);
          
          // 检查是否为阿里云函数结果
          if (testMethod.includes('AliyunFC') || testMethod.includes('Real Cloud Function')) {
            if (result.status === 'success' && ping < 1000) {
              console.log(`   ⚠️ 警告: 阿里云函数显示Google可访问，延迟${ping}ms - 这可能不准确`);
            } else if (result.status !== 'success' || ping >= 5000) {
              console.log(`   ✅ 正确: 阿里云函数显示Google无法访问或延迟极高`);
            }
          }
        });
        
        // 统计分析
        const successResults = data.results.filter(r => r.status === 'success');
        const lowLatencyResults = successResults.filter(r => (r.ping || r.latency || 5000) < 1000);
        
        console.log('\n📈 结果分析:');
        console.log(`总节点数: ${data.results.length}`);
        console.log(`成功节点数: ${successResults.length}`);
        console.log(`低延迟节点数 (<1000ms): ${lowLatencyResults.length}`);
        
        if (lowLatencyResults.length > 0) {
          console.log('\n⚠️ 准确性问题:');
          console.log(`有 ${lowLatencyResults.length} 个节点显示Google可以快速访问，这在中国大陆是不准确的`);
          
          console.log('\n🔧 可能的原因:');
          console.log('1. 某些数据源使用了代理或CDN');
          console.log('2. 测试方法不够严格');
          console.log('3. 使用了模拟数据而非真实网络测试');
        } else {
          console.log('\n✅ 准确性验证:');
          console.log('所有节点都正确显示Google无法访问或延迟极高');
        }
        
      } else {
        console.log('⚠️ 没有返回测试结果');
      }
    } else {
      console.log(`❌ API调用失败: HTTP ${response.status}`);
      const errorText = await response.text();
      console.log(`错误信息: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
  }

  console.log('\n💡 下一步建议:');
  console.log('1. 如果仍有不准确结果，需要进一步检查数据源');
  console.log('2. 确保只使用真实的阿里云函数数据');
  console.log('3. 验证阿里云函数的网络测试逻辑');
}

// 同时测试百度作为对比
async function testBaiduComparison() {
  console.log('\n🔍 对比测试: 百度 vs Google');
  console.log('=' .repeat(50));
  
  const targets = [
    { name: '百度', url: 'https://baidu.com', expected: '低延迟' },
    { name: 'Google', url: 'https://www.google.com', expected: '高延迟或无法访问' }
  ];
  
  for (const target of targets) {
    console.log(`\n📊 测试 ${target.name} (${target.url})`);
    console.log(`🎯 预期: ${target.expected}`);
    
    try {
      const response = await fetch('http://localhost:3003/api/ping-cloudping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: target.url }),
        timeout: 30000
      });
      
      if (response.ok) {
        const data = await response.json();
        const results = data.results || [];
        const aliyunResults = results.filter(r => 
          r.testMethod?.includes('AliyunFC') || 
          r.testMethod?.includes('Real Cloud Function')
        );
        
        if (aliyunResults.length > 0) {
          console.log(`📍 阿里云函数结果 (${aliyunResults.length}个节点):`);
          aliyunResults.forEach(result => {
            const ping = result.ping || result.latency || 'N/A';
            const status = result.status === 'success' ? '✅' : '❌';
            const city = result.location?.city || result.city || 'Unknown';
            console.log(`   ${status} ${city}: ${ping}ms`);
          });
          
          const avgLatency = aliyunResults
            .filter(r => r.status === 'success')
            .reduce((sum, r) => sum + (r.ping || r.latency || 0), 0) / 
            aliyunResults.filter(r => r.status === 'success').length;
          
          console.log(`📊 平均延迟: ${avgLatency ? avgLatency.toFixed(1) : 'N/A'}ms`);
        } else {
          console.log('⚠️ 没有阿里云函数结果');
        }
      }
    } catch (error) {
      console.log(`❌ ${target.name} 测试失败: ${error.message}`);
    }
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

// 主函数
async function main() {
  await testGoogleAccuracy();
  await testBaiduComparison();
  
  console.log('\n🎉 测试完成！');
  console.log('如果结果仍然不准确，请检查阿里云函数的网络测试逻辑');
}

main().catch(console.error);
