'use client';

import { useState } from 'react';

interface TestResult {
  target: string;
  results: Array<{
    city: string;
    province: string;
    latency: number;
    status: string;
  }>;
  timestamp: string;
}

export default function TestAccuracyPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTarget, setSelectedTarget] = useState('www.google.com');

  const testTargets = [
    { value: 'www.google.com', label: 'Google (被墙网站)' },
    { value: 'www.baidu.com', label: '百度 (国内网站)' },
    { value: 'github.com', label: 'GitHub (国外网站)' },
    { value: 'www.taobao.com', label: '淘宝 (国内网站)' },
    { value: 'stackoverflow.com', label: 'Stack Overflow (开发者网站)' }
  ];

  // 客户端延迟测试函数 - 优化版本，减少等待时间
  const performClientLatencyTest = async (target: string): Promise<number | null> => {
    try {
      // 使用Resource Timing API进行更准确的测试
      const url = target.startsWith('http') ? target : `https://${target}`;
      const startTime = performance.now();

      // 创建一个图片元素来测试连接延迟
      const img = new Image();

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve(null);
        }, 2000); // 从5秒减少到2秒

        img.onload = img.onerror = () => {
          clearTimeout(timeout);
          const endTime = performance.now();
          resolve(Math.round(endTime - startTime));
        };

        // 使用favicon或小图片进行测试
        img.src = `${url}/favicon.ico?t=${Date.now()}`;
      });
    } catch (error) {
      console.warn('客户端延迟测试失败:', error);
      return null;
    }
  };

  const runAccuracyTest = async () => {
    setIsLoading(true);
    try {
      console.log(`🧪 开始准确性测试: ${selectedTarget}`);

      // 执行客户端延迟测试以获取混合测试数据
      let hybridResult = null;
      try {
        console.log('📊 执行客户端延迟测试...');
        const clientLatency = await performClientLatencyTest(selectedTarget);
        console.log('📊 客户端延迟测试结果:', clientLatency);

        if (clientLatency !== null && clientLatency > 0) {
          hybridResult = {
            httpLatency: null,
            wsLatency: null,
            resourceLatency: clientLatency, // 使用Resource Timing结果
            averageLatency: clientLatency,
            testMethod: 'Resource Timing API'
          };
          console.log('✅ 混合测试数据准备完成:', hybridResult);
        } else {
          console.log('⚠️ 客户端测试无效结果，将使用后端智能模拟');
        }
      } catch (error) {
        console.warn('⚠️ 客户端延迟测试失败，使用后端模式:', error);
      }

      console.log('🚀 发送API请求...');
      const response = await fetch('/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: selectedTarget,
          maxNodes: 34,
          useBatchTesting: false,
          useHybridTest: hybridResult !== null,
          hybridResult: hybridResult
        })
      });

      console.log('📡 API响应状态:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API错误响应:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      
      if (data.success && data.results) {
        const newResult: TestResult = {
          target: selectedTarget,
          results: data.results.map((r: any) => ({
            city: r.node,
            province: r.location.province,
            latency: r.ping,
            status: r.status
          })),
          timestamp: new Date().toLocaleString('zh-CN')
        };
        
        setTestResults(prev => [newResult, ...prev.slice(0, 4)]); // 保留最近5次测试
        console.log('✅ 测试完成:', newResult);
      }
    } catch (error) {
      console.error('❌ 测试失败:', error);
      alert('测试失败: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeResults = (results: TestResult['results']) => {
    const latencies = results.filter(r => r.status === 'success').map(r => r.latency);
    if (latencies.length === 0) return null;

    const min = Math.min(...latencies);
    const max = Math.max(...latencies);
    const avg = Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length);
    const variance = Math.round(max - min);

    return { min, max, avg, variance, count: latencies.length };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            🧪 Ping测试准确性验证
          </h1>
          <p className="text-gray-600 mb-6">
            测试新的延迟算法，验证不同网站在各省份的延迟分布是否合理
          </p>

          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <select
              value={selectedTarget}
              onChange={(e) => setSelectedTarget(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {testTargets.map(target => (
                <option key={target.value} value={target.value}>
                  {target.label}
                </option>
              ))}
            </select>
            
            <button
              onClick={runAccuracyTest}
              disabled={isLoading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? '测试中...' : '开始测试'}
            </button>
          </div>
        </div>

        {testResults.map((result, index) => {
          const analysis = analyzeResults(result.results);
          
          return (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  📊 {result.target} - {result.timestamp}
                </h2>
                {analysis && (
                  <div className="text-sm text-gray-600">
                    平均: {analysis.avg}ms | 范围: {analysis.min}-{analysis.max}ms | 差值: {analysis.variance}ms
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                {result.results.map((city, cityIndex) => (
                  <div
                    key={cityIndex}
                    className={`p-3 rounded-lg border ${
                      city.status === 'success'
                        ? city.latency < 50
                          ? 'bg-green-50 border-green-200'
                          : city.latency < 150
                          ? 'bg-yellow-50 border-yellow-200'
                          : city.latency < 300
                          ? 'bg-orange-50 border-orange-200'
                          : 'bg-red-50 border-red-200'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="font-medium text-gray-800">{city.city}</div>
                    <div className="text-sm text-gray-600">{city.province}</div>
                    <div className={`text-lg font-bold ${
                      city.status === 'success'
                        ? city.latency < 50
                          ? 'text-green-600'
                          : city.latency < 150
                          ? 'text-yellow-600'
                          : city.latency < 300
                          ? 'text-orange-600'
                          : 'text-red-600'
                        : 'text-gray-500'
                    }`}>
                      {city.status === 'success' ? `${city.latency}ms` : '超时'}
                    </div>
                  </div>
                ))}
              </div>

              {analysis && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold text-gray-800 mb-2">📈 统计分析</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">成功节点:</span>
                      <span className="ml-2 font-medium">{analysis.count}/34</span>
                    </div>
                    <div>
                      <span className="text-gray-600">最小延迟:</span>
                      <span className="ml-2 font-medium text-green-600">{analysis.min}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-600">最大延迟:</span>
                      <span className="ml-2 font-medium text-red-600">{analysis.max}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-600">延迟差值:</span>
                      <span className="ml-2 font-medium">{analysis.variance}ms</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {testResults.length === 0 && (
          <div className="bg-white rounded-xl shadow-lg p-12 text-center">
            <div className="text-gray-400 text-lg mb-2">🎯</div>
            <p className="text-gray-600">选择一个测试目标，点击"开始测试"查看延迟分布</p>
          </div>
        )}

        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">💡 测试说明</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>被墙网站 (如Google):</strong> 大陆地区应显示高延迟(600-1000ms)或超时，港澳台地区延迟较低</p>
            <p><strong>国内网站 (如百度、淘宝):</strong> 全国延迟应较低(12-80ms)，一线城市更优</p>
            <p><strong>国外网站 (如GitHub):</strong> 延迟中等(80-200ms)，地理位置和网络基础设施影响明显</p>
            <p><strong>延迟分布:</strong> 应该看到明显的地区差异，而不是所有省份都相同</p>
          </div>
        </div>
      </div>
    </div>
  );
}
