// 阿里云监控API集成 - 服务端专用

// 阿里云监控API集成 - 简化版本
export class AliyunMonitorIntegration {
  private accessKeyId: string;
  private accessKeySecret: string;
  private endpoint = 'https://cms.cn-hangzhou.aliyuncs.com';
  private version = '2019-01-01';

  constructor() {
    this.accessKeyId = process.env.ALIYUN_ACCESS_KEY_ID || '';
    this.accessKeySecret = process.env.ALIYUN_ACCESS_KEY_SECRET || '';
  }

  // 检查是否已配置API密钥
  isConfigured(): boolean {
    return !!(this.accessKeyId && this.accessKeySecret);
  }

  // 创建站点监控任务
  async createSiteMonitor(url: string, taskName: string) {
    if (!this.isConfigured()) {
      throw new Error('阿里云API密钥未配置');
    }

    const params = {
      Action: 'CreateSiteMonitor',
      TaskName: taskName,
      Address: url,
      TaskType: 'HTTP',
      Interval: '5', // 5分钟间隔
      IspCities: this.getChineseCities(),
      OptionsJson: JSON.stringify({
        http_method: 'GET',
        timeout: 30000,
        request_content: '',
        match_rule: 0
      })
    };

    try {
      const result = await this.makeRequest(params);
      return {
        success: true,
        taskId: result.TaskId,
        message: '站点监控任务创建成功'
      };
    } catch (error) {
      console.error('创建站点监控失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 获取站点监控数据
  async getSiteMonitorData(taskId: string, days: number = 7) {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: '阿里云API密钥未配置'
      };
    }

    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - days * 24 * 60 * 60 * 1000);

    const params = {
      Action: 'DescribeMetricList',
      Namespace: 'acs_networkmonitor',
      MetricName: 'Availability',
      Dimensions: JSON.stringify([{ taskId }]),
      StartTime: startTime.toISOString(),
      EndTime: endTime.toISOString(),
      Period: '300' // 5分钟间隔
    };

    try {
      const result = await this.makeRequest(params);
      return this.processSiteMonitorData(result);
    } catch (error) {
      console.error('获取站点监控数据失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 获取网站性能统计
  async getWebsiteStats(url: string) {
    // 简化版本：返回模拟数据，实际项目中需要真实API调用
    if (!this.isConfigured()) {
      return {
        success: false,
        error: '阿里云API密钥未配置'
      };
    }

    // 模拟数据，实际需要调用真实API
    return {
      success: true,
      data: {
        url,
        availability: 99.2,
        avgResponseTime: 120,
        errorRate: 0.8,
        lastWeekTrend: 'stable',
        regionalData: [
          { region: '北京', avgLatency: 115, availability: 99.5 },
          { region: '上海', avgLatency: 108, availability: 99.3 },
          { region: '广州', avgLatency: 125, availability: 99.1 },
          { region: '深圳', avgLatency: 122, availability: 99.2 }
        ]
      }
    };
  }

  // 处理站点监控数据
  private processSiteMonitorData(rawData: any) {
    try {
      const datapoints = rawData.Datapoints || [];
      
      if (datapoints.length === 0) {
        return {
          success: true,
          data: {
            availability: 0,
            avgResponseTime: 0,
            dataPoints: []
          }
        };
      }

      const totalPoints = datapoints.length;
      const successPoints = datapoints.filter((point: any) => point.Value > 0).length;
      const availability = (successPoints / totalPoints) * 100;
      
      const avgResponseTime = datapoints.reduce((sum: number, point: any) => {
        return sum + (point.ResponseTime || 0);
      }, 0) / totalPoints;

      return {
        success: true,
        data: {
          availability: Math.round(availability * 100) / 100,
          avgResponseTime: Math.round(avgResponseTime),
          dataPoints: datapoints.map((point: any) => ({
            timestamp: point.Timestamp,
            value: point.Value,
            responseTime: point.ResponseTime
          }))
        }
      };
    } catch (error) {
      return {
        success: false,
        error: '数据处理失败'
      };
    }
  }

  // 生成API签名
  private generateSignature(params: any, method: string = 'GET') {
    // 简化版本：在实际项目中需要在服务端实现完整的签名算法
    // 这里返回一个模拟签名，实际使用时需要调用服务端API
    return 'mock-signature-' + Date.now();
  }

  // 发送API请求
  private async makeRequest(params: any) {
    const commonParams = {
      AccessKeyId: this.accessKeyId,
      SignatureMethod: 'HMAC-SHA1',
      SignatureVersion: '1.0',
      SignatureNonce: Math.random().toString(36).substring(2),
      Timestamp: new Date().toISOString(),
      Format: 'JSON',
      Version: this.version,
      ...params
    };

    // 生成签名
    const signature = this.generateSignature(commonParams);
    commonParams.Signature = signature;

    // 构造请求URL
    const queryString = Object.keys(commonParams)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(commonParams[key])}`)
      .join('&');

    const url = `${this.endpoint}?${queryString}`;

    // 发送请求
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.Code) {
      throw new Error(`API错误: ${result.Code} - ${result.Message}`);
    }

    return result;
  }

  // 获取中国主要城市监控点
  private getChineseCities() {
    return [
      '北京-联通',
      '北京-电信',
      '北京-移动',
      '上海-联通',
      '上海-电信',
      '上海-移动',
      '广州-联通',
      '广州-电信',
      '深圳-联通',
      '深圳-电信',
      '杭州-联通',
      '杭州-电信',
      '成都-联通',
      '成都-电信',
      '西安-联通',
      '西安-电信'
    ];
  }
}

// 创建单例实例
export const aliyunMonitor = new AliyunMonitorIntegration();

// 增强版ping测试函数
export async function enhancedPingTest(target: string) {
  try {
    // 1. 执行现有的实时测试
    const realTimeResponse = await fetch('/api/ping-cloudping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target })
    });
    
    const realTimeResults = await realTimeResponse.json();

    // 2. 尝试获取阿里云监控历史数据
    let historicalData = null;
    if (aliyunMonitor.isConfigured()) {
      try {
        const stats = await aliyunMonitor.getWebsiteStats(target);
        if (stats.success) {
          historicalData = stats.data;
        }
      } catch (error) {
        console.warn('获取历史数据失败:', error);
      }
    }

    // 3. 合并结果
    return {
      success: true,
      realTime: realTimeResults,
      historical: historicalData,
      enhanced: !!historicalData
    };
  } catch (error) {
    console.error('增强ping测试失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

export default AliyunMonitorIntegration;
