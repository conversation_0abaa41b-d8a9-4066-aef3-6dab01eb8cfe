'use client';

import React, { useState } from 'react';
import { Zap, TrendingUp, AlertCircle, CheckCircle, Clock, Globe, ChevronUp, ChevronDown } from 'lucide-react';

interface EnhancedPingResult {
  success: boolean;
  target: string;
  realTime: any;
  historical: any;
  recommendations: string[];
  features: {
    monitoringEnabled: boolean;
    historicalDataAvailable: boolean;
    recommendationsGenerated: boolean;
  };
}

interface EnhancedPingTesterProps {
  isDarkMode: boolean;
}

const EnhancedPingTester: React.FC<EnhancedPingTesterProps> = ({ isDarkMode }) => {
  const [target, setTarget] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<EnhancedPingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  const getPingColor = (ping: number, status: string): string => {
    if (status !== 'success') return '#dc2626';
    if (ping <= 50) return '#16a34a';
    if (ping <= 100) return '#22c55e';
    if (ping <= 200) return '#84cc16';
    if (ping <= 250) return '#eab308';
    return '#ea580c';
  };

  const handleTest = async () => {
    if (!target.trim()) {
      setError('请输入要测试的网址');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/enhanced-ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ target }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setResult(data);
      } else {
        setError(data.error || '测试失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络请求失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      handleTest();
    }
  };

  return (
    <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Zap className={`h-6 w-6 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
          <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            增强版Ping测试
          </h2>
          <span className={`px-2 py-1 text-xs rounded-full ${isDarkMode ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800'}`}>
            Beta
          </span>
        </div>

        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-300'
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
          title={isExpanded ? '收缩' : '展开'}
        >
          {isExpanded ? (
            <ChevronUp className="h-5 w-5" />
          ) : (
            <ChevronDown className="h-5 w-5" />
          )}
        </button>
      </div>

      {isExpanded && (
        <>
          <div className="mb-6">
            <div className="flex space-x-2">
              <input
                type="text"
                value={target}
                onChange={(e) => setTarget(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入要测试的网址，如: https://example.com"
                className={`flex-1 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
                disabled={isLoading}
              />
              <button
                onClick={handleTest}
                disabled={isLoading}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : isDarkMode
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {isLoading ? '测试中...' : '开始测试'}
              </button>
            </div>
          </div>

          {error && (
            <div className={`mb-6 p-4 rounded-lg border-l-4 ${isDarkMode ? 'bg-red-900 border-red-500 text-red-300' : 'bg-red-50 border-red-500 text-red-700'}`}>
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {result && (
            <div className="space-y-6">
              <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  功能状态
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className={`h-5 w-5 ${result.features.monitoringEnabled ? 'text-green-500' : 'text-gray-400'}`} />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                      云监控: {result.features.monitoringEnabled ? '已启用' : '未配置'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className={`h-5 w-5 ${result.features.historicalDataAvailable ? 'text-green-500' : 'text-gray-400'}`} />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                      历史数据: {result.features.historicalDataAvailable ? '可用' : '不可用'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className={`h-5 w-5 ${result.features.recommendationsGenerated ? 'text-green-500' : 'text-gray-400'}`} />
                    <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                      智能建议: {result.features.recommendationsGenerated ? '已生成' : '无建议'}
                    </span>
                  </div>
                </div>
              </div>

              {result.realTime && (
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <h3 className={`text-lg font-semibold mb-3 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    <Clock className="h-5 w-5 mr-2" />
                    实时测试结果
                  </h3>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {result.realTime.results?.slice(0, 8).map((item: any, index: number) => (
                      <div key={index} className={`p-3 rounded border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                        <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {item.node}
                        </div>
                        <div
                          className="text-sm font-medium"
                          style={{ color: getPingColor(item.ping || 0, item.status) }}
                        >
                          {item.status === 'success' ? `${item.ping}ms` : '超时'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {result.historical && (
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <h3 className={`text-lg font-semibold mb-3 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    <TrendingUp className="h-5 w-5 mr-2" />
                    历史监控数据
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className={`p-3 rounded border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>可用率</div>
                      <div className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {result.historical.availability}%
                      </div>
                    </div>
                    <div className={`p-3 rounded border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>平均延迟</div>
                      <div className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {result.historical.avgResponseTime}ms
                      </div>
                    </div>
                    <div className={`p-3 rounded border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>错误率</div>
                      <div className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {result.historical.errorRate}%
                      </div>
                    </div>
                  </div>

                  {result.historical.regionalData && (
                    <div>
                      <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        地域性能对比
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {result.historical.regionalData.map((region: any, index: number) => (
                          <div key={index} className={`p-2 rounded text-sm border ${isDarkMode ? 'bg-gray-600 border-gray-500' : 'bg-white border-gray-200'}`}>
                            <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                              {region.region}
                            </div>
                            <div
                              className="font-medium"
                              style={{ color: getPingColor(region.avgLatency, 'success') }}
                            >
                              {region.avgLatency}ms
                            </div>
                            <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              可用率: {region.availability}%
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {result.recommendations && result.recommendations.length > 0 && (
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-blue-900' : 'bg-blue-50'}`}>
                  <h3 className={`text-lg font-semibold mb-3 flex items-center ${isDarkMode ? 'text-blue-300' : 'text-blue-900'}`}>
                    <Globe className="h-5 w-5 mr-2" />
                    智能建议
                  </h3>
                  <ul className="space-y-2">
                    {result.recommendations.map((recommendation, index) => (
                      <li key={index} className={`${isDarkMode ? 'text-blue-200' : 'text-blue-800'}`}>
                        {recommendation}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default EnhancedPingTester;