@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-400 {
  scrollbar-color: #9CA3AF transparent;
}

.scrollbar-track-gray-200 {
  scrollbar-color: #9CA3AF #F3F4F6;
}

/* Webkit浏览器滚动条样式 */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #F3F4F6;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #9CA3AF;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* 暗色模式滚动条 */
.dark .scrollbar-thin::-webkit-scrollbar-track {
  background: #1F2937;
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background: #4B5563;
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* 节点状态滚动动画 */
@keyframes scroll-up {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.animate-scroll-up {
  animation: scroll-up 15s linear infinite;
}

/* 暂停动画在悬停时 */
.animate-scroll-up:hover {
  animation-play-state: paused;
}
