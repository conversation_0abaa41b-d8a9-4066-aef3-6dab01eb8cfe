#!/bin/bash

# 快速测试阿里云函数的脚本

echo "🚀 快速测试阿里云函数"
echo "===================="

# 您的阿里云函数URL
BEIJING_URL="https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-beijing/ping-function-beijing/"
SHANGHAI_URL="https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shanghai/ping-function-shanghai/"

echo "🔍 测试北京节点..."
echo "URL: $BEIJING_URL"
echo "请求数据: {\"target\":\"https://baidu.com\"}"
echo ""

curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"target":"https://baidu.com"}' \
  "$BEIJING_URL" \
  --max-time 15 \
  --silent \
  --show-error | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo ""
echo "🔍 测试上海节点..."
echo "URL: $SHANGHAI_URL"
echo ""

curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"target":"https://baidu.com"}' \
  "$SHANGHAI_URL" \
  --max-time 15 \
  --silent \
  --show-error | jq '.' 2>/dev/null || echo "响应不是有效的JSON格式"

echo ""
echo "✅ 测试完成！"
echo ""
echo "💡 如果看到包含 latency、provider、city 等字段的JSON响应，说明函数正常工作"
echo "💡 如果看到错误信息，请检查函数部署状态"
