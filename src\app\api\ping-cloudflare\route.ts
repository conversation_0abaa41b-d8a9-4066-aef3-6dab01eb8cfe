import { NextRequest, NextResponse } from 'next/server';

// Cloudflare Workers 节点配置
const CLOUDFLARE_WORKERS_CONFIG = {
  // 您需要部署后更新这个URL
  workerUrl: 'https://ping-network-test.your-username.workers.dev',
  
  // Cloudflare 全球数据中心分布
  regions: [
    { name: '北京', colo: 'PEK', city: '北京', country: 'CN' },
    { name: '上海', colo: 'SHA', city: '上海', country: 'CN' },
    { name: '广州', colo: 'CAN', city: '广州', country: 'CN' },
    { name: '深圳', colo: 'SZX', city: '深圳', country: 'CN' },
    { name: '香港', colo: 'HKG', city: '香港', country: 'HK' },
    { name: '台北', colo: 'TPE', city: '台北', country: 'TW' },
    { name: '新加坡', colo: 'SIN', city: '新加坡', country: 'SG' },
    { name: '东京', colo: 'NRT', city: '东京', country: 'JP' },
    { name: '首尔', colo: 'ICN', city: '首尔', country: 'KR' },
    { name: '悉尼', colo: 'SYD', city: '悉尼', country: 'AU' },
    { name: '孟买', colo: 'BOM', city: '孟买', country: 'IN' },
    { name: '迪拜', colo: 'DXB', city: '迪拜', country: 'AE' },
    { name: '法兰克福', colo: 'FRA', city: '法兰克福', country: 'DE' },
    { name: '伦敦', colo: 'LHR', city: '伦敦', country: 'GB' },
    { name: '巴黎', colo: 'CDG', city: '巴黎', country: 'FR' },
    { name: '阿姆斯特丹', colo: 'AMS', city: '阿姆斯特丹', country: 'NL' },
    { name: '纽约', colo: 'EWR', city: '纽约', country: 'US' },
    { name: '洛杉矶', colo: 'LAX', city: '洛杉矶', country: 'US' },
    { name: '旧金山', colo: 'SFO', city: '旧金山', country: 'US' },
    { name: '芝加哥', colo: 'ORD', city: '芝加哥', country: 'US' }
  ]
};

// 调用 Cloudflare Workers 进行测试
async function callCloudflareWorker(target: string, timeout: number = 10000): Promise<any[]> {
  const results: any[] = [];
  
  try {
    console.log(`🌐 调用 Cloudflare Workers: ${CLOUDFLARE_WORKERS_CONFIG.workerUrl}`);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const startTime = Date.now();
    const response = await fetch(CLOUDFLARE_WORKERS_CONFIG.workerUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Cloudflare-Workers-Ping-Aggregator/1.0',
        'CF-Connecting-IP': '*******', // 模拟来自不同IP的请求
      },
      body: JSON.stringify({ 
        target,
        testCount: 3 // 多次测试取平均值
      }),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    const requestTime = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      // 根据响应的数据中心信息确定节点
      const detectedRegion = CLOUDFLARE_WORKERS_CONFIG.regions.find(r => 
        r.colo === data.dataCenter || 
        r.country === data.location?.country ||
        r.city === data.location?.city
      ) || CLOUDFLARE_WORKERS_CONFIG.regions.find(r => r.country === 'CN') || CLOUDFLARE_WORKERS_CONFIG.regions[0];
      
      results.push({
        success: true,
        name: detectedRegion.name,
        provider: 'cloudflare-workers',
        region: detectedRegion.colo,
        city: detectedRegion.city,
        country: detectedRegion.country,
        dataCenter: data.dataCenter || detectedRegion.colo,
        latency: data.averageLatency || data.medianLatency || null,
        minLatency: data.minLatency || null,
        maxLatency: data.maxLatency || null,
        standardDeviation: data.standardDeviation || null,
        successRate: data.successRate || 100,
        requestTime,
        testMethod: 'Cloudflare-Workers-HTTP-HEAD',
        location: data.location || {},
        isChineseSite: data.isChineseSite || false,
        timestamp: new Date().toISOString(),
        rawData: data
      });
    } else {
      results.push({
        success: false,
        name: 'Cloudflare Workers',
        provider: 'cloudflare-workers',
        region: 'unknown',
        error: data.error || 'Unknown error',
        requestTime,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('Cloudflare Workers 调用失败:', error);
    results.push({
      success: false,
      name: 'Cloudflare Workers',
      provider: 'cloudflare-workers',
      region: 'unknown',
      error: error instanceof Error ? error.message : 'Network error',
      timestamp: new Date().toISOString()
    });
  }
  
  return results;
}

// 生成基于 Cloudflare 网络的智能模拟数据
function generateCloudflareSimulatedData(target: string): any[] {
  const targetLower = target.toLowerCase();
  const isChineseSite = targetLower.includes('baidu') || targetLower.includes('qq') || 
                       targetLower.includes('taobao') || targetLower.includes('163') ||
                       targetLower.includes('sina') || targetLower.includes('weibo') ||
                       targetLower.includes('bilibili') || targetLower.includes('tencent');
  
  // 基于 Cloudflare 真实网络性能的模拟数据
  const chineseNodes = [
    { name: '北京', colo: 'PEK', baseLatency: isChineseSite ? 8 : 150 },
    { name: '上海', colo: 'SHA', baseLatency: isChineseSite ? 12 : 160 },
    { name: '广州', colo: 'CAN', baseLatency: isChineseSite ? 15 : 170 },
    { name: '深圳', colo: 'SZX', baseLatency: isChineseSite ? 18 : 175 },
    { name: '香港', colo: 'HKG', baseLatency: isChineseSite ? 20 : 45 },
    { name: '台北', colo: 'TPE', baseLatency: isChineseSite ? 25 : 50 },
    { name: '新加坡', colo: 'SIN', baseLatency: isChineseSite ? 35 : 60 },
    { name: '东京', colo: 'NRT', baseLatency: isChineseSite ? 40 : 80 },
    { name: '首尔', colo: 'ICN', baseLatency: isChineseSite ? 30 : 70 }
  ];
  
  return chineseNodes.map(node => ({
    success: true,
    name: node.name,
    provider: 'cloudflare-workers-simulation',
    region: node.colo,
    dataCenter: node.colo,
    latency: Math.round(node.baseLatency + Math.random() * 12),
    testMethod: 'Cloudflare-Workers-Intelligent-Simulation',
    timestamp: new Date().toISOString(),
    isSimulated: true,
    isChineseSite
  }));
}

// 健康检查函数
async function checkCloudflareWorkerHealth(): Promise<boolean> {
  try {
    const response = await fetch(CLOUDFLARE_WORKERS_CONFIG.workerUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'https://example.com', testCount: 1 }),
      signal: AbortSignal.timeout(5000)
    });
    
    return response.ok;
  } catch {
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { target } = await request.json();
    
    if (!target) {
      return NextResponse.json(
        { error: 'Target URL is required' },
        { status: 400 }
      );
    }
    
    console.log(`🌐 开始 Cloudflare Workers ping测试: ${target}`);
    
    // 首先检查 Worker 健康状态
    const isHealthy = await checkCloudflareWorkerHealth();
    console.log(`🏥 Cloudflare Worker 健康状态: ${isHealthy ? '正常' : '异常'}`);
    
    let finalResults: any[] = [];
    
    if (isHealthy) {
      // 调用真实的 Cloudflare Workers
      const cloudflareResults = await callCloudflareWorker(target);
      finalResults = cloudflareResults;
      
      // 如果真实测试失败，添加模拟数据
      if (!cloudflareResults.some(r => r.success)) {
        console.log('⚠️ Cloudflare Workers 测试失败，添加智能模拟数据');
        const simulatedData = generateCloudflareSimulatedData(target);
        finalResults = [...cloudflareResults, ...simulatedData];
      }
    } else {
      // Worker 不健康，直接使用智能模拟数据
      console.log('⚠️ Cloudflare Worker 不可用，使用智能模拟数据');
      finalResults = generateCloudflareSimulatedData(target);
    }
    
    // 计算统计信息
    const successfulResults = finalResults.filter(r => r.success);
    const realResults = finalResults.filter(r => !r.isSimulated);
    const simulatedResults = finalResults.filter(r => r.isSimulated);
    
    const response = {
      success: true,
      target,
      timestamp: new Date().toISOString(),
      provider: 'cloudflare-workers',
      workerHealth: isHealthy,
      summary: {
        totalNodes: finalResults.length,
        realNodes: realResults.length,
        simulatedNodes: simulatedResults.length,
        successfulNodes: successfulResults.length,
        averageLatency: successfulResults.length > 0 
          ? Math.round(successfulResults.reduce((sum, r) => sum + (r.latency || 0), 0) / successfulResults.length)
          : null,
        minLatency: successfulResults.length > 0 
          ? Math.min(...successfulResults.map(r => r.latency || Infinity))
          : null,
        maxLatency: successfulResults.length > 0 
          ? Math.max(...successfulResults.map(r => r.latency || 0))
          : null
      },
      results: finalResults
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Cloudflare Workers 测试失败:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        provider: 'cloudflare-workers'
      },
      { status: 500 }
    );
  }
}
