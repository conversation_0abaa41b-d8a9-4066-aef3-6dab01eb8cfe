#!/usr/bin/env node

// 本地测试阿里云函数的详细脚本
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// 您的真实阿里云函数URL
const ALIYUN_FUNCTIONS = [
  {
    name: '北京',
    region: 'cn-beijing',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-beijing/ping-function-beijing/',
    city: '北京'
  },
  {
    name: '上海', 
    region: 'cn-shanghai',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shanghai/ping-function-shanghai/',
    city: '上海'
  },
  {
    name: '深圳',
    region: 'cn-shenzhen', 
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shenzhen/ping-function-shenzhen/',
    city: '深圳'
  },
  {
    name: '杭州',
    region: 'cn-hangzhou',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-hangzhou/ping-function-hangzhou/',
    city: '杭州'
  },
  {
    name: '青岛',
    region: 'cn-qingdao',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-qingdao/ping-function-qingdao/',
    city: '青岛'
  }
];

// 测试函数可用性
async function testFunctionAvailability(func) {
  console.log(`\n🔍 测试 ${func.name} 函数可用性...`);
  console.log(`📍 地区: ${func.region}`);
  console.log(`🌐 URL: ${func.url}`);
  
  try {
    // 1. 测试GET请求（基本连通性）
    console.log('   📡 测试基本连通性...');
    const getResponse = await fetch(func.url, {
      method: 'GET',
      timeout: 10000
    });
    
    console.log(`   📊 GET响应状态: ${getResponse.status}`);
    
    // 2. 测试POST请求（实际功能）
    console.log('   🎯 测试ping功能...');
    const startTime = Date.now();
    
    const postResponse = await fetch(func.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target: 'https://baidu.com'
      }),
      timeout: 15000
    });
    
    const requestTime = Date.now() - startTime;
    console.log(`   ⏱️ 请求耗时: ${requestTime}ms`);
    console.log(`   📊 POST响应状态: ${postResponse.status}`);
    
    if (postResponse.ok) {
      const data = await postResponse.json();
      console.log(`   ✅ 函数执行成功!`);
      console.log(`   📈 测试延迟: ${data.latency}ms`);
      console.log(`   🏷️ 提供商: ${data.provider}`);
      console.log(`   🌍 地区: ${data.region}`);
      console.log(`   🏙️ 城市: ${data.city}`);
      console.log(`   🔧 测试方法: ${data.testMethod}`);
      console.log(`   📝 请求ID: ${data.requestId}`);
      
      if (data.details && data.details.length > 0) {
        console.log(`   📋 详细测试结果:`);
        data.details.forEach((detail, index) => {
          const status = detail.success ? '✅' : '❌';
          console.log(`      ${index + 1}. ${detail.method}: ${detail.latency}ms ${status}`);
        });
      }
      
      return {
        success: true,
        latency: data.latency,
        requestTime: requestTime,
        data: data
      };
    } else {
      const errorText = await postResponse.text();
      console.log(`   ❌ 函数执行失败`);
      console.log(`   📄 错误响应: ${errorText.substring(0, 200)}...`);
      
      return {
        success: false,
        error: `HTTP ${postResponse.status}`,
        requestTime: requestTime
      };
    }
    
  } catch (error) {
    console.log(`   ❌ 连接失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      requestTime: 0
    };
  }
}

// 批量测试所有函数
async function testAllFunctions() {
  console.log('🚀 开始测试所有阿里云函数...');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const func of ALIYUN_FUNCTIONS) {
    const result = await testAllFunctions(func);
    results.push({
      ...func,
      ...result
    });
    
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 生成测试报告
  console.log('\n📊 测试报告汇总');
  console.log('=' .repeat(60));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`✅ 成功函数: ${successCount}/${totalCount}`);
  console.log(`📈 成功率: ${(successCount / totalCount * 100).toFixed(1)}%`);
  
  if (successCount > 0) {
    const avgLatency = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.latency, 0) / successCount;
    
    const avgRequestTime = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.requestTime, 0) / successCount;
    
    console.log(`📊 平均延迟: ${avgLatency.toFixed(1)}ms`);
    console.log(`⏱️ 平均请求时间: ${avgRequestTime.toFixed(1)}ms`);
    
    console.log('\n🏆 函数性能排行:');
    results
      .filter(r => r.success)
      .sort((a, b) => a.latency - b.latency)
      .forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.name}: ${result.latency}ms`);
      });
  }
  
  console.log('\n❌ 失败的函数:');
  results
    .filter(r => !r.success)
    .forEach(result => {
      console.log(`   - ${result.name}: ${result.error}`);
    });
  
  return results;
}

// 测试特定目标网站
async function testSpecificTarget(target) {
  console.log(`\n🎯 测试特定目标: ${target}`);
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const func of ALIYUN_FUNCTIONS) {
    console.log(`\n📍 ${func.name} 测试 ${target}...`);
    
    try {
      const response = await fetch(func.url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target }),
        timeout: 15000
      });
      
      if (response.ok) {
        const data = await response.json();
        results.push({
          city: func.name,
          success: data.success,
          latency: data.latency,
          details: data.details
        });
        
        const status = data.success ? '✅' : '❌';
        console.log(`   ${status} ${data.latency}ms`);
      } else {
        console.log(`   ❌ HTTP ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ ${error.message}`);
    }
  }
  
  return results;
}

// 主函数
async function main() {
  console.log('🔍 阿里云函数本地测试工具');
  console.log('=' .repeat(60));
  
  // 获取命令行参数
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // 测试特定目标
    const target = args[0];
    await testSpecificTarget(target);
  } else {
    // 测试所有函数
    await testAllFunctions();
    
    console.log('\n💡 使用提示:');
    console.log('测试特定网站: node scripts/test-aliyun-local.js https://example.com');
    console.log('查看函数日志: fun logs -s ping-service-beijing -f ping-function-beijing');
    console.log('访问控制台: https://fc.console.aliyun.com/');
  }
}

// 修复函数名错误
async function testAllFunctions() {
  console.log('🚀 开始测试所有阿里云函数...');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const func of ALIYUN_FUNCTIONS) {
    const result = await testFunctionAvailability(func);
    results.push({
      ...func,
      ...result
    });
    
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 生成测试报告
  console.log('\n📊 测试报告汇总');
  console.log('=' .repeat(60));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`✅ 成功函数: ${successCount}/${totalCount}`);
  console.log(`📈 成功率: ${(successCount / totalCount * 100).toFixed(1)}%`);
  
  if (successCount > 0) {
    const avgLatency = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.latency, 0) / successCount;
    
    const avgRequestTime = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.requestTime, 0) / successCount;
    
    console.log(`📊 平均延迟: ${avgLatency.toFixed(1)}ms`);
    console.log(`⏱️ 平均请求时间: ${avgRequestTime.toFixed(1)}ms`);
    
    console.log('\n🏆 函数性能排行:');
    results
      .filter(r => r.success)
      .sort((a, b) => a.latency - b.latency)
      .forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.name}: ${result.latency}ms`);
      });
  }
  
  console.log('\n❌ 失败的函数:');
  results
    .filter(r => !r.success)
    .forEach(result => {
      console.log(`   - ${result.name}: ${result.error}`);
    });
  
  return results;
}

// 运行主函数
main().catch(console.error);
