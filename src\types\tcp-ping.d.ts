declare module 'tcp-ping' {
  export interface PingOptions {
    address: string;
    port?: number;
    attempts?: number;
    timeout?: number;
  }
  export interface PingResult {
    avg: number;
    max: number;
    min: number;
    results: number[];
    address: string;
    port: number;
    attempts: number;
    time: number;
  }
  function ping(options: PingOptions, callback: (err: Error | null, data: PingResult) => void): void;
  export { ping };
  export default { ping };
}
