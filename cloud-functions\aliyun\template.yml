ROSTemplateFormatVersion: '2015-09-01'
Transform: 'Aliyun::Serverless-2018-04-03'
Resources:
  ping-service-beijing:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 北京节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-beijing:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '北京节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '北京'
          PROVINCE: '北京'
          APP_REGION: 'cn-beijing'  # 修改为 APP_REGION
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-shanghai:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 上海节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-shanghai:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '上海节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '上海'
          PROVINCE: '上海'
          APP_REGION: 'cn-shanghai'  # 修改为 APP_REGION
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-shenzhen:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 深圳节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-shenzhen:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '深圳节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '深圳'
          PROVINCE: '广东'
          APP_REGION: 'cn-shenzhen'  # 修改为 APP_REGION
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-hangzhou:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 杭州节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-hangzhou:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '杭州节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '杭州'
          PROVINCE: '浙江'
          APP_REGION: 'cn-hangzhou'  # 修改为 APP_REGION
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-qingdao:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 青岛节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-qingdao:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '青岛节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '青岛'
          PROVINCE: '山东'
          APP_REGION: 'cn-qingdao'  # 修改为 APP_REGION
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-guangzhou:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 广州节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-guangzhou:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '广州节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '广州'
          PROVINCE: '广东'
          APP_REGION: 'cn-guangzhou'
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-chengdu:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 成都节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-chengdu:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '成都节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '成都'
          PROVINCE: '四川'
          APP_REGION: 'cn-chengdu'
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-chongqing:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 重庆节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-chongqing:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '重庆节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '重庆'
          PROVINCE: '重庆'
          APP_REGION: 'cn-chongqing'
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']

  ping-service-nanjing:
    Type: 'Aliyun::Serverless::Service'
    Properties:
      Description: 'Ping测试服务 - 南京节点'
      Policies:
        - AliyunFCInvocationAccess
    ping-function-nanjing:
      Type: 'Aliyun::Serverless::Function'
      Properties:
        Handler: index.handler
        Runtime: nodejs10
        CodeUri: ./
        Description: '南京节点真实网络延迟测试'
        MemorySize: 512
        Timeout: 30
        EnvironmentVariables:
          CITY: '南京'
          PROVINCE: '江苏'
          APP_REGION: 'cn-nanjing'
      Events:
        httpTrigger:
          Type: HTTP
          Properties:
            AuthType: ANONYMOUS
            Methods: ['GET', 'POST']