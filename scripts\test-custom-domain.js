#!/usr/bin/env node

/**
 * 测试自定义域名是否正常工作
 */

const https = require('https');

// 测试配置
const TEST_URLS = [
  'https://ping-api.wobys.dpdns.org?target=https://baidu.com',
  'https://ping-api.wobys.dpdns.org?target=https://baidu.com'
];

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求封装
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: 'GET',
      timeout: 10000
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers,
            parseError: e.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// 测试单个URL
async function testUrl(url, name) {
  const startTime = Date.now();
  
  try {
    colorLog('cyan', `\n🔍 测试 ${name}`);
    colorLog('blue', `   URL: ${url}`);
    
    const response = await makeRequest(url);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    if (response.status === 200 && response.data && response.data.success) {
      colorLog('green', `   ✅ 成功 - 延迟: ${response.data.latency}ms, 总耗时: ${totalTime}ms`);
      
      if (response.data.cloudflare) {
        colorLog('blue', `   📍 数据中心: ${response.data.cloudflare.datacenter || 'Unknown'}`);
      }
      
      return { success: true, latency: response.data.latency, totalTime };
    } else {
      colorLog('red', `   ❌ 失败 - HTTP ${response.status}`);
      if (response.data && response.data.error) {
        colorLog('yellow', `   错误: ${response.data.error}`);
      }
      return { success: false, error: `HTTP ${response.status}` };
    }
    
  } catch (error) {
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    colorLog('red', `   ❌ 请求失败: ${error.message} (${totalTime}ms)`);
    return { success: false, error: error.message };
  }
}

// 主测试函数
async function runTests() {
  colorLog('cyan', '🌐 自定义域名测试');
  colorLog('cyan', '='.repeat(50));
  
  const results = [];
  
  // 测试自定义域名
  const customResult = await testUrl(TEST_URLS[0], '自定义域名 (ping-api.wobys.dpdns.org)');
  results.push({ name: '自定义域名', ...customResult });
  
  // 测试默认Workers域名
  const defaultResult = await testUrl(TEST_URLS[1], '默认Workers域名 (ping-network-test.wob21.workers.dev)');
  results.push({ name: '默认域名', ...defaultResult });
  
  // 显示总结
  colorLog('cyan', '\n📊 测试总结');
  colorLog('cyan', '='.repeat(50));
  
  results.forEach(result => {
    const status = result.success ? '✅ 正常' : '❌ 失败';
    const latency = result.latency ? ` (${result.latency}ms)` : '';
    colorLog(result.success ? 'green' : 'red', `${result.name}: ${status}${latency}`);
    
    if (!result.success && result.error) {
      colorLog('yellow', `  错误: ${result.error}`);
    }
  });
  
  // 建议
  colorLog('cyan', '\n💡 建议');
  colorLog('cyan', '='.repeat(50));
  
  const customSuccess = results[0].success;
  const defaultSuccess = results[1].success;
  
  if (customSuccess && defaultSuccess) {
    colorLog('green', '✅ 两个域名都正常工作，可以使用自定义域名');
    colorLog('blue', '   建议: 在环境变量中保持使用自定义域名');
  } else if (!customSuccess && defaultSuccess) {
    colorLog('yellow', '⚠️  自定义域名有问题，建议使用默认域名');
    colorLog('blue', '   建议: 将 CLOUDFLARE_WORKER_URL 改为默认域名');
  } else if (customSuccess && !defaultSuccess) {
    colorLog('green', '✅ 自定义域名正常，默认域名可能有问题');
    colorLog('blue', '   建议: 继续使用自定义域名');
  } else {
    colorLog('red', '❌ 两个域名都有问题，请检查Cloudflare Workers部署');
  }
  
  return results;
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    colorLog('red', `测试脚本错误: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests };
