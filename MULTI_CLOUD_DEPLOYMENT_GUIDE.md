# 🌐 多云部署指南 - Vercel Edge Functions + Cloudflare Workers

## 📋 概述

本指南将帮助您部署两个免费的云服务方案，实现全球网络延迟测试：

- **⭐⭐⭐⭐⭐ Vercel Edge Functions** - 亚太地区优化，免费用户支持香港节点
- **⭐⭐⭐⭐⭐ Cloudflare Workers** - 全球节点，支持中国大陆服务器

## 🚀 1. Vercel Edge Functions 部署

### 1.1 配置说明

**免费用户限制：**
- 只能选择一个区域（推荐香港 HKG1）
- 代码中已配置多个亚太区域作为备选
- 升级付费版本后，所有区域会自动生效

**支持的亚太区域：**
```javascript
regions: [
  'hkg1', // 🇭🇰 香港 (东部) - 免费用户首选
  'icn1', // 🇰🇷 首尔, 韩国 (东北部)
  'nrt1', // 🇯🇵 东京, 日本 (东北部)
  'sin1', // 🇸🇬 新加坡 (东南部)
  'syd1', // 🇦🇺 悉尼, 澳大利亚 (东南部)
  'bom1'  // 🇮🇳 孟买, 印度 (南部)
]
```

### 1.2 部署步骤

1. **确认文件存在：**
   ```
   api/ping-vercel-edge.js
   src/app/api/ping-vercel-edge/route.ts
   ```

2. **Vercel 项目设置：**
   - 登录 Vercel Dashboard
   - 进入项目设置 → Function Regions
   - 选择 "Hong Kong (East) - ap-east-1 - hkg1"

3. **部署验证：**
   ```bash
   # 本地测试
   curl "https://your-domain.vercel.app/api/ping-vercel-edge?target=https://baidu.com"
   ```

## 🌐 2. Cloudflare Workers 部署

### 2.1 配置说明

**支持的中国大陆及全球节点：**
```javascript
// 🇨🇳 中国大陆地区 (优先选择)
'SHA': '上海, 中国',     // 中国东部
'HKG': '香港, 中国',     // 中国香港
'TPE': '台北, 台湾',     // 台湾

// 🌏 亚太地区 (靠近中国)
'NRT': '东京, 日本',     // 日本
'ICN': '首尔, 韩国',     // 韩国
'SIN': '新加坡',         // 新加坡
// ... 更多全球节点
```

### 2.2 部署步骤

1. **安装 Wrangler CLI：**
   ```bash
   npm install -g wrangler
   ```

2. **登录 Cloudflare：**
   ```bash
   wrangler login
   ```

3. **部署 Worker：**
   ```bash
   # 开发环境部署
   wrangler deploy --env development
   
   # 生产环境部署
   wrangler deploy --env production
   ```

4. **获取 Worker URL：**
   ```
   https://ping-network-test.your-subdomain.workers.dev
   ```

5. **配置环境变量：**
   ```bash
   # 在 Vercel 项目中添加环境变量
   CLOUDFLARE_WORKER_URL=https://ping-network-test.wob21.workers.dev
   ```

## ⚙️ 3. 环境变量配置

### 3.1 Vercel 环境变量

在 Vercel Dashboard → Settings → Environment Variables 中添加：

```env
# Cloudflare Workers URL
CLOUDFLARE_WORKER_URL=https://ping-network-test.wob21.workers.dev

# 多云测试配置
ENABLE_MULTI_CLOUD_TESTING=true
VERCEL_EDGE_REGIONS=hkg1,sin1,icn1,nrt1
CLOUDFLARE_PREFERRED_REGIONS=SHA,HKG,TPE,NRT,ICN,SIN
```

### 3.2 Cloudflare Workers 环境变量

在 `wrangler.toml` 中已配置：

```toml
[vars]
SERVICE_NAME = "Ping Test Worker"
CHINA_MAINLAND_SUPPORT = "true"
SUPPORTED_REGIONS = "SHA,BJS,CAN,CDG,NKG,WUH,XMN,HGH,CSX,HRB,CGO,TAO,SJW,TYN,LHW,KWE,NNG,KMG,HFE,FOC,HET,SHE,DLC,TSN,JZH,XNN,URC,YCU,ZUH,HKG,MFM,TPE,NRT,ICN,SIN,KUL,BKK,MNL,SYD,BOM,LAX,SFO,SEA,DEN,ORD,IAD,JFK,YYZ,LHR,FRA,AMS,ARN,DME,CAI,JNB,GRU,SCL"
```

## 🧪 4. 测试验证

### 4.1 功能测试

1. **Vercel Edge Functions 测试：**
   ```bash
   curl -X POST "https://your-domain.vercel.app/api/ping-vercel-edge" \
     -H "Content-Type: application/json" \
     -d '{"target":"https://baidu.com"}'
   ```

2. **Cloudflare Workers 测试：**
   ```bash
   curl "https://ping-network-test.your-subdomain.workers.dev?target=https://baidu.com"
   ```

### 4.2 UI 测试

1. 访问主页面
2. 启用 "🌐 多云测试 (Vercel + Cloudflare)" 选项
3. 输入测试URL，点击开始测试
4. 查看结果中是否包含：
   - `香港 (Vercel Edge)` 节点
   - `上海, 中国 (Cloudflare)` 或其他CF节点

## 📊 5. 性能优化

### 5.1 延迟校准

系统会根据不同云服务的特性自动校准延迟：

```javascript
// Vercel Edge Functions - 香港节点优化
if (region === 'hkg1') {
  calibratedLatency = rawLatency * 0.8; // 香港节点延迟较低
}

// Cloudflare Workers - 中国大陆节点优化
if (['SHA', 'HKG', 'TPE'].includes(colo)) {
  calibratedLatency = Math.max(rawLatency * 0.8, 10);
}
```

### 5.2 智能路由

系统会根据测试结果推荐最优的云服务：

- **国内网站** → 优先使用 Cloudflare Workers (中国大陆节点)
- **国外网站** → 优先使用 Vercel Edge Functions (香港节点)

## 🔧 6. 故障排除

### 6.1 常见问题

**Q: Vercel Edge Functions 只显示香港节点？**
A: 免费用户限制，升级付费版本可使用所有亚太节点。

**Q: Cloudflare Workers 部署失败？**
A: 检查 `wrangler.toml` 配置和登录状态：
```bash
wrangler whoami
wrangler deploy --dry-run
```

**Q: 多云测试结果不准确？**
A: 检查网络环境和目标网站的可访问性。

### 6.2 调试模式

启用调试模式查看详细日志：

```javascript
// 在浏览器控制台中
localStorage.setItem('debug-multi-cloud', 'true');
```

## 📈 7. 监控和分析

### 7.1 性能指标

系统会收集以下指标：

- **延迟分布** - 不同云服务的延迟对比
- **成功率** - 各云服务的可用性
- **地理覆盖** - 全球节点的覆盖情况

### 7.2 数据导出

测试结果会自动保存到历史记录，支持：

- JSON 格式导出
- CSV 格式导出
- 图表可视化

## 🎯 8. 下一步计划

- [ ] 添加更多云服务提供商 (AWS Lambda@Edge, Azure Functions)
- [ ] 实现智能负载均衡
- [ ] 添加实时监控面板
- [ ] 支持自定义测试节点

---

## 📞 技术支持

如有问题，请检查：
1. 环境变量配置是否正确
2. 云服务是否正常部署
3. 网络连接是否稳定

**部署完成后，您的ping测试工具将支持：**
- 🇭🇰 香港 Vercel Edge Functions 节点
- 🇨🇳 中国大陆 Cloudflare Workers 节点
- 🌏 全球多个亚太地区节点
- 🚀 智能路由和延迟优化
