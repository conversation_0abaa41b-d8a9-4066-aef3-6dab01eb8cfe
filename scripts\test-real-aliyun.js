#!/usr/bin/env node

// 测试真实阿里云函数部署
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testRealAliyunFunctions() {
  console.log('🌐 测试真实阿里云函数部署');
  console.log('=' .repeat(50));

  // 真实的阿里云函数端点
  const aliyunNodes = [
    { name: '北京', endpoint: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-beijing/ping-function-beijing/', region: 'cn-beijing' },
    { name: '上海', endpoint: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shanghai/ping-function-shanghai/', region: 'cn-shanghai' },
    { name: '深圳', endpoint: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shenzhen/ping-function-shenzhen/', region: 'cn-shenzhen' },
    { name: '杭州', endpoint: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-hangzhou/ping-function-hangzhou/', region: 'cn-hangzhou' },
    { name: '青岛', endpoint: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-qingdao/ping-function-qingdao/', region: 'cn-qingdao' }
  ];

  const testTargets = [
    'https://baidu.com',
    'https://chatgpt.com',
    'https://github.com'
  ];

  for (const target of testTargets) {
    console.log(`\n📊 测试目标: ${target}`);
    console.log('-' .repeat(40));

    const results = [];

    for (const node of aliyunNodes) {
      try {
        console.log(`🔍 测试 ${node.name} (${node.region})...`);
        
        const startTime = Date.now();
        const response = await fetch(node.endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ target }),
          timeout: 10000
        });

        const requestTime = Date.now() - startTime;

        if (response.ok) {
          const data = await response.json();
          results.push({
            name: node.name,
            region: node.region,
            success: data.success,
            latency: data.latency,
            requestTime: requestTime,
            provider: data.provider,
            testMethod: data.testMethod,
            details: data.details
          });
          
          const status = data.success ? '✅' : '❌';
          console.log(`   ${status} ${node.name}: ${data.latency}ms (请求耗时: ${requestTime}ms)`);
          
          if (data.details && data.details.length > 0) {
            data.details.forEach(detail => {
              console.log(`      - ${detail.method}: ${detail.latency}ms ${detail.success ? '✅' : '❌'}`);
            });
          }
        } else {
          console.log(`   ❌ ${node.name}: HTTP ${response.status}`);
          const errorText = await response.text();
          console.log(`      错误: ${errorText.substring(0, 100)}...`);
        }
      } catch (error) {
        console.log(`   ❌ ${node.name}: ${error.message}`);
      }
    }

    // 汇总结果
    console.log(`\n📈 ${target} 测试汇总:`);
    const successCount = results.filter(r => r.success).length;
    const avgLatency = results.filter(r => r.success).reduce((sum, r) => sum + r.latency, 0) / successCount || 0;
    const avgRequestTime = results.reduce((sum, r) => sum + r.requestTime, 0) / results.length || 0;
    
    console.log(`   ✅ 成功节点: ${successCount}/${results.length}`);
    console.log(`   📊 平均延迟: ${avgLatency.toFixed(1)}ms`);
    console.log(`   ⏱️ 平均请求时间: ${avgRequestTime.toFixed(1)}ms`);
    
    // 显示最快和最慢的节点
    const successResults = results.filter(r => r.success);
    if (successResults.length > 0) {
      const fastest = successResults.reduce((min, r) => r.latency < min.latency ? r : min);
      const slowest = successResults.reduce((max, r) => r.latency > max.latency ? r : max);
      
      console.log(`   🚀 最快节点: ${fastest.name} (${fastest.latency}ms)`);
      console.log(`   🐌 最慢节点: ${slowest.name} (${slowest.latency}ms)`);
    }
  }

  // 测试集成API
  console.log('\n🔗 测试集成API...');
  console.log('=' .repeat(50));
  
  try {
    const response = await fetch('http://localhost:3001/api/ping-cloudping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        target: 'https://baidu.com',
        maxNodes: 15,
        useHybridTest: false,
        useBatchTesting: false
      }),
      timeout: 30000
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 集成API测试成功');
      console.log(`📊 测试类型: ${data.metadata?.testType}`);
      console.log(`🌐 数据源: ${data.metadata?.dataSource}`);
      console.log(`📍 总节点数量: ${data.results?.length || 0}`);
      
      if (data.results && data.results.length > 0) {
        // 统计不同数据源的结果
        const aliyunResults = data.results.filter(r => 
          r.testMethod?.includes('AliyunFC') || 
          r.location?.network?.includes('Aliyun')
        );
        const uptimeResults = data.results.filter(r => 
          r.testMethod?.includes('UptimeRobot')
        );
        const globalpingResults = data.results.filter(r => 
          r.testMethod?.includes('Globalping')
        );
        
        console.log(`☁️ 阿里云函数结果: ${aliyunResults.length}`);
        console.log(`📊 UptimeRobot结果: ${uptimeResults.length}`);
        console.log(`🌐 Globalping结果: ${globalpingResults.length}`);
        
        if (aliyunResults.length > 0) {
          console.log('\n🎯 阿里云函数测试结果:');
          aliyunResults.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.location?.city || 'Unknown'}: ${result.ping}ms (${result.testMethod})`);
          });
        }
      }
    } else {
      console.log(`❌ 集成API测试失败: HTTP ${response.status}`);
      const errorText = await response.text();
      console.log(`错误信息: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`❌ 集成API测试失败: ${error.message}`);
  }

  console.log('\n🎉 真实阿里云函数测试完成！');
  console.log('=' .repeat(50));
  console.log('✅ 您现在拥有了真正的多云部署！');
  console.log('🌐 访问 http://localhost:3001 体验真实的网络测试');
  console.log('📊 测试结果现在包含真实的阿里云函数数据');
}

// 运行测试
testRealAliyunFunctions().catch(console.error);
