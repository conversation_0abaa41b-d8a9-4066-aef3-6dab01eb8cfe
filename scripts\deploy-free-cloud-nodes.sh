#!/bin/bash

# 部署免费云节点脚本
# 自动部署到Vercel、Cloudflare Workers、Railway、Fly.io

echo "🚀 开始部署免费云节点..."
echo "=================================="

# 检查必要的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装: $2"
        return 1
    else
        echo "✅ $1 已安装"
        return 0
    fi
}

echo "🔍 检查部署工具..."
check_tool "vercel" "npm install -g vercel"
check_tool "wrangler" "npm install -g wrangler"
check_tool "railway" "npm install -g @railway/cli"
check_tool "flyctl" "curl -L https://fly.io/install.sh | sh"

echo ""
echo "📋 部署计划:"
echo "1. Vercel Edge Functions (全球10+节点)"
echo "2. Cloudflare Workers (全球200+节点)"
echo "3. Railway (美国节点)"
echo "4. Fly.io (全球分布式节点)"
echo ""

# 1. 部署到Vercel
echo "🔨 1. 部署到Vercel..."
echo "----------------------------------------"

if command -v vercel &> /dev/null; then
    echo "📤 部署Vercel Edge Function..."
    
    # 检查是否已登录
    if ! vercel whoami &> /dev/null; then
        echo "🔐 请先登录Vercel:"
        vercel login
    fi
    
    # 部署项目
    vercel --prod
    
    if [ $? -eq 0 ]; then
        echo "✅ Vercel部署成功"
        VERCEL_URL=$(vercel ls | grep "https://" | head -1 | awk '{print $2}')
        echo "🌐 Vercel URL: $VERCEL_URL"
    else
        echo "❌ Vercel部署失败"
    fi
else
    echo "⚠️ 跳过Vercel部署 (工具未安装)"
fi

echo ""

# 2. 部署到Cloudflare Workers
echo "🔨 2. 部署到Cloudflare Workers..."
echo "----------------------------------------"

if command -v wrangler &> /dev/null; then
    echo "📤 部署Cloudflare Worker..."
    
    # 检查是否已登录
    if ! wrangler whoami &> /dev/null; then
        echo "🔐 请先登录Cloudflare:"
        wrangler login
    fi
    
    # 创建wrangler.toml配置
    cat > wrangler.toml << EOF
name = "ping-worker"
main = "cloudflare-workers/ping-worker.js"
compatibility_date = "2024-01-01"

[env.production]
name = "ping-worker"
EOF
    
    # 部署Worker
    wrangler deploy
    
    if [ $? -eq 0 ]; then
        echo "✅ Cloudflare Workers部署成功"
        echo "🌐 Worker URL: https://ping-worker.your-domain.workers.dev"
    else
        echo "❌ Cloudflare Workers部署失败"
    fi
else
    echo "⚠️ 跳过Cloudflare Workers部署 (工具未安装)"
fi

echo ""

# 3. 部署到Railway
echo "🔨 3. 部署到Railway..."
echo "----------------------------------------"

if command -v railway &> /dev/null; then
    echo "📤 部署到Railway..."
    
    # 检查是否已登录
    if ! railway whoami &> /dev/null; then
        echo "🔐 请先登录Railway:"
        railway login
    fi
    
    # 创建Railway项目
    railway link
    
    # 部署项目
    railway up
    
    if [ $? -eq 0 ]; then
        echo "✅ Railway部署成功"
        RAILWAY_URL=$(railway status | grep "https://" | awk '{print $2}')
        echo "🌐 Railway URL: $RAILWAY_URL"
    else
        echo "❌ Railway部署失败"
    fi
else
    echo "⚠️ 跳过Railway部署 (工具未安装)"
fi

echo ""

# 4. 部署到Fly.io
echo "🔨 4. 部署到Fly.io..."
echo "----------------------------------------"

if command -v flyctl &> /dev/null; then
    echo "📤 部署到Fly.io..."
    
    # 检查是否已登录
    if ! flyctl auth whoami &> /dev/null; then
        echo "🔐 请先登录Fly.io:"
        flyctl auth login
    fi
    
    # 创建fly.toml配置
    cat > fly.toml << EOF
app = "ping-test-app"
primary_region = "nrt"

[build]
  builder = "heroku/buildpacks:20"

[[services]]
  http_checks = []
  internal_port = 3000
  processes = ["app"]
  protocol = "tcp"
  script_checks = []

  [services.concurrency]
    hard_limit = 25
    soft_limit = 20
    type = "connections"

  [[services.ports]]
    force_https = true
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [[services.tcp_checks]]
    grace_period = "1s"
    interval = "15s"
    restart_limit = 0
    timeout = "2s"
EOF
    
    # 部署应用
    flyctl deploy
    
    if [ $? -eq 0 ]; then
        echo "✅ Fly.io部署成功"
        FLY_URL=$(flyctl status | grep "https://" | awk '{print $2}')
        echo "🌐 Fly.io URL: $FLY_URL"
    else
        echo "❌ Fly.io部署失败"
    fi
else
    echo "⚠️ 跳过Fly.io部署 (工具未安装)"
fi

echo ""
echo "🎉 免费云节点部署完成！"
echo "=================================="

echo ""
echo "📊 部署总结:"
echo "✅ 免费云服务提供商: 4个"
echo "🌐 全球节点覆盖: 200+个数据中心"
echo "💰 总成本: $0 (完全免费)"
echo "📈 月度免费额度:"
echo "   - Vercel: 100万次函数调用"
echo "   - Cloudflare: 300万次请求"
echo "   - Railway: $5额度"
echo "   - Fly.io: 3个免费应用"

echo ""
echo "🔧 下一步操作:"
echo "1. 更新 src/app/api/ping-free-nodes/route.ts 中的端点URL"
echo "2. 测试各个节点的连通性"
echo "3. 集成到主应用的ping测试功能"

echo ""
echo "💡 使用建议:"
echo "- Cloudflare Workers: 中国大陆访问最快"
echo "- Vercel Edge: 全球覆盖最好"
echo "- Railway: 美国节点稳定"
echo "- Fly.io: 边缘计算性能最佳"
