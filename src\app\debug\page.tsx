'use client';

import { useState } from 'react';

export default function DebugPage() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testCloudPing = async (target: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target,
          maxNodes: 34
        })
      });

      const data = await response.json();
      setResults(data);
      console.log('CloudPing Results:', data);
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">CloudPing Debug</h1>
      
      <div className="space-y-4">
        <button
          onClick={() => testCloudPing('https://www.baidu.com')}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Test Baidu (国内网站)
        </button>
        
        <button
          onClick={() => testCloudPing('https://chatgpt.com')}
          disabled={loading}
          className="px-4 py-2 bg-red-500 text-white rounded disabled:opacity-50"
        >
          Test ChatGPT (被墙网站)
        </button>
        
        <button
          onClick={() => testCloudPing('https://google.com')}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
        >
          Test Google (被墙网站)
        </button>
      </div>

      {loading && (
        <div className="mt-4 text-blue-600">Testing...</div>
      )}

      {results && (
        <div className="mt-8">
          <h2 className="text-xl font-bold mb-4">Results ({results.results?.length || 0} nodes)</h2>
          
          <div className="mb-4">
            <strong>Success Rate:</strong> {results.summary?.successRate || 'N/A'}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {results.results?.map((result: any, index: number) => (
              <div key={index} className="border p-4 rounded">
                <div className="font-bold">{result.node}</div>
                <div className="text-sm text-gray-600">
                  {result.location?.city || result.node} - {result.location?.province || '未知省份'}
                </div>
                <div className={`text-lg ${result.status === 'success' ? 'text-green-600' : 'text-red-600'}`}>
                  {result.status === 'success' ? `${result.ping}ms` : result.status}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  节点: {result.node}<br/>
                  时间: {new Date(result.timestamp).toLocaleTimeString()}<br/>
                  {result.testMethod && `方法: ${result.testMethod}`}<br/>
                  {result.testEndpoint && `端点: ${result.testEndpoint}`}<br/>
                  {result.location && `地区: ${result.location.region || '未知'}`}<br/>
                  {result.location && `网络: ${result.location.network || '未知'}`}
                </div>
              </div>
            ))}
          </div>
          
          <details className="mt-8">
            <summary className="cursor-pointer font-bold">Raw JSON</summary>
            <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto">
              {JSON.stringify(results, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}
