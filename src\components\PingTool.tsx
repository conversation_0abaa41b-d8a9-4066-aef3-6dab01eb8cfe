'use client';

import React, { useState, useEffect } from 'react';
import ChinaMap from './ChinaMap';
import VisitCounter from './VisitCounter';
import WebRTCTester from './WebRTCTester';
import EnhancedNetworkTester, { EnhancedTestResult } from './EnhancedNetworkTester';
import SmartRouting from './SmartRouting';
import PerformanceMonitor from './PerformanceMonitor';
import ExtendedMetrics, { NetworkMetrics } from './ExtendedMetrics';
import { historyStorage } from '../utils/HistoryStorage';
import { networkTestCache } from '../utils/CacheManager';
import { batchTestManager, performanceMonitor } from '../utils/BatchTestManager';
// 临时注释掉新服务以确保部署成功
// import { UptimeRobotService, NetworkMonitoringAggregator } from '../services/UptimeRobotService';
// import { WebRTCLatencyTest, AdvancedNetworkTester } from '../services/WebRTCLatencyTest';
// import { MultiCloudNetworkTester } from '../services/MultiCloudDeployment';
// import { NetworkDataCollector, NetworkLatencyPredictor } from '../services/NetworkDataCalibration';



interface PingToolProps {
  isDarkMode: boolean;
}

interface PingResult {
  node: string;
  ping: number;
  status: 'success' | 'timeout' | 'error';
  timestamp: number;
  location?: {
    country: string;
    city: string;
    province: string;
    region: string;
    latitude: number;
    longitude: number;
    asn: number;
    network: string;
  };
  testMethod?: string;
  testEndpoint?: string;
}

const PingTool: React.FC<PingToolProps> = ({ isDarkMode }) => {
  const [target, setTarget] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [pingResults, setPingResults] = useState<PingResult[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'map' | 'monitor' | 'routing' | 'metrics'>('map');
  const [testProgress, setTestProgress] = useState(0);
  const [testStage, setTestStage] = useState('');
  const [enhancedResults, setEnhancedResults] = useState<EnhancedTestResult | null>(null);
  const [networkMetrics, setNetworkMetrics] = useState<NetworkMetrics | null>(null);
  const [useEnhancedTesting, setUseEnhancedTesting] = useState(true);
  const [useBatchTesting, setUseBatchTesting] = useState(true);

  // 临时注释掉新服务实例以确保部署成功
  // const [dataCollector] = useState(() => new NetworkDataCollector());
  // const [latencyPredictor] = useState(() => new NetworkLatencyPredictor(dataCollector));
  // const [multiCloudTester] = useState(() => new MultiCloudNetworkTester());
  // const [advancedTester] = useState(() => new AdvancedNetworkTester());
  // const [webrtcTester, setWebrtcTester] = useState<WebRTCLatencyTest | null>(null);

  // 监控服务配置
  // const [monitoringAggregator] = useState(() => {
  //   return new NetworkMonitoringAggregator({
  //     uptimeRobotApiKey: process.env.NEXT_PUBLIC_UPTIMEROBOT_API_KEY
  //   });
  // });






  // 完整的中国省份城市测试节点列表 - 与CloudPing API后端保持完全一致
  // 每个省份只显示一个代表节点，与后端的节点选择逻辑匹配
  const testNodes = [
    // 直辖市
    { name: '北京', province: '北京' },
    { name: '上海', province: '上海' },
    { name: '天津', province: '天津' },
    { name: '重庆', province: '重庆' },

    // 省会城市（每个省份一个代表节点）
    { name: '石家庄', province: '河北' },
    { name: '太原', province: '山西' },
    { name: '呼和浩特', province: '内蒙古' },
    { name: '沈阳', province: '辽宁' },
    { name: '长春', province: '吉林' },
    { name: '哈尔滨', province: '黑龙江' },
    { name: '南京', province: '江苏' },
    { name: '杭州', province: '浙江' },
    { name: '合肥', province: '安徽' },
    { name: '福州', province: '福建' },
    { name: '南昌', province: '江西' },
    { name: '济南', province: '山东' },
    { name: '郑州', province: '河南' },
    { name: '武汉', province: '湖北' },
    { name: '长沙', province: '湖南' },
    { name: '广州', province: '广东' },
    { name: '海口', province: '海南' },
    { name: '南宁', province: '广西' },
    { name: '成都', province: '四川' },
    { name: '贵阳', province: '贵州' },
    { name: '昆明', province: '云南' },
    { name: '拉萨', province: '西藏' },
    { name: '西安', province: '陕西' },
    { name: '兰州', province: '甘肃' },
    { name: '西宁', province: '青海' },
    { name: '银川', province: '宁夏' },
    { name: '乌鲁木齐', province: '新疆' },

    // 港澳台地区
    { name: '香港', province: '香港' },
    { name: '澳门', province: '澳门' },
    { name: '台北', province: '台湾' }
  ];

  // 根据ping值获取颜色（用于文字显示）
  const getPingColor = (ping: number, status: string) => {
    if (status !== 'success') return '#dc2626'; // 超时 - 红色
    if (ping <= 50) return '#16a34a';           // ≤50ms - 深绿
    if (ping <= 100) return '#22c55e';          // 51-100ms - 绿色
    if (ping <= 200) return '#84cc16';          // 101-200ms - 浅绿
    if (ping <= 250) return '#eab308';          // 201-250ms - 黄色
    return '#ea580c';                           // >250ms - 橙色
  };

  // 根据ping值获取背景色类名（用于卡片背景）
  const getPingBgClass = (ping: number, status: string) => {
    if (status !== 'success') return 'bg-red-600'; // 超时 - 红色背景
    if (ping <= 50) return 'bg-green-700';         // ≤50ms - 深绿背景
    if (ping <= 100) return 'bg-green-600';        // 51-100ms - 绿色背景
    if (ping <= 200) return 'bg-lime-500';         // 101-200ms - 浅绿背景
    if (ping <= 250) return 'bg-yellow-500';       // 201-250ms - 黄色背景
    return 'bg-orange-600';                        // >250ms - 橙色背景
  };

  // 模拟ping测试函数 - 针对被墙网站的真实情况
  const simulatePing = async (nodeInfo: {name: string, province: string}, targetUrl: string): Promise<PingResult> => {
    return new Promise((resolve) => {
      // 模拟网络延迟
      const delay = Math.random() * 2000 + 500; // 500-2500ms

      setTimeout(() => {
        // 检查目标URL是否为被墙网站
        const blockedDomains = [
          'chatgpt.com',
          'openai.com',
          'google.com',
          'youtube.com',
          'facebook.com',
          'twitter.com',
          'instagram.com',
          'github.com',
          'stackoverflow.com'
        ];

        const isBlocked = blockedDomains.some(domain => targetUrl.includes(domain));

        if (isBlocked) {
          // 被墙网站在中国大陆应该全部超时
          // 只有香港、澳门、台湾可能可以访问
          if (['香港', '澳门', '台北'].includes(nodeInfo.name)) {
            // 港澳台地区可以访问，但延迟较高
            const latency = Math.random() * 100 + 150; // 150-250ms
            resolve({
              node: nodeInfo.name,
              ping: Math.round(latency),
              status: 'success',
              timestamp: Date.now(),
              location: {
                country: nodeInfo.province === '香港' ? 'Hong Kong' :
                        nodeInfo.province === '澳门' ? 'Macau' :
                        nodeInfo.province === '台湾' ? 'Taiwan' : 'China',
                city: nodeInfo.name,
                province: nodeInfo.province,
                region: nodeInfo.province === '香港' || nodeInfo.province === '澳门' || nodeInfo.province === '台湾' ? '港澳台' : '华南',
                latitude: 0,
                longitude: 0,
                asn: 0,
                network: 'Simulation'
              }
            });
          } else {
            // 中国大陆地区全部超时
            resolve({
              node: nodeInfo.name,
              ping: 0,
              status: 'timeout',
              timestamp: Date.now(),
              location: {
                country: 'China',
                city: nodeInfo.name,
                province: nodeInfo.province,
                region: '华中',
                latitude: 0,
                longitude: 0,
                asn: 0,
                network: 'Simulation'
              }
            });
          }
          return;
        }

        // 对于非被墙网站，模拟正常的网络状况
        const baseLatency = Math.random() * 200 + 20; // 20-220ms基础延迟

        // 根据地区调整延迟（模拟真实网络情况）
        let adjustedLatency = baseLatency;

        // 一线城市网络较好
        if (['北京', '上海', '广州', '深圳', '杭州', '南京'].includes(nodeInfo.name)) {
          adjustedLatency *= 0.7;
        }
        // 西部地区网络稍慢
        else if (['乌鲁木齐', '拉萨', '西宁', '兰州', '银川'].includes(nodeInfo.name)) {
          adjustedLatency *= 1.5;
        }
        // 偏远地区偶尔有超时
        else if (['拉萨', '乌鲁木齐'].includes(nodeInfo.name) && Math.random() < 0.2) {
          resolve({
            node: nodeInfo.name,
            ping: 0,
            status: 'timeout',
            timestamp: Date.now(),
            location: {
              country: 'China',
              city: nodeInfo.name,
              province: nodeInfo.province,
              region: '西北',
              latitude: 0,
              longitude: 0,
              asn: 0,
              network: 'Simulation'
            }
          });
          return;
        }

        const finalLatency = Math.round(adjustedLatency);

        resolve({
          node: nodeInfo.name,
          ping: finalLatency,
          status: 'success',
          timestamp: Date.now(),
          location: {
            country: 'China',
            city: nodeInfo.name,
            province: nodeInfo.province,
            region: '华中',
            latitude: 0,
            longitude: 0,
            asn: 0,
            network: 'Simulation'
          }
        });
      }, delay);
    });
  };






  // 🌐 真实数据测试：使用UptimeRobot真实监控数据
  const performRealDataTest = async () => {
    if (!target.trim() || isRunning) return;

    setIsRunning(true);
    setPingResults([]);
    setTestStage('使用UptimeRobot真实数据测试');
    setTestProgress(0);

    try {


      setTestProgress(20);

      const response = await fetch('/api/ping-uptimerobot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ target: target.trim() }),
      });

      setTestProgress(60);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      setTestProgress(80);

      if (data.success && data.results) {

        setPingResults(data.results);
        setTestStage(`真实数据测试完成 - 数据源: ${data.metadata?.dataSource || 'UptimeRobot'}`);
      } else {

        setTestStage(`真实数据测试失败: ${data.error || '未知错误'}`);

        // 如果真实数据失败，回退到普通测试

        await startPing();
        return;
      }

      setTestProgress(100);

    } catch (error) {
      setTestStage(`真实数据测试错误: ${error instanceof Error ? error.message : '未知错误'}`);

      // 回退到普通测试
      await startPing();
    } finally {
      setIsRunning(false);
    }
  };

  // 🚀 新版本：智能多方法网络测试
  const performIntelligentNetworkTest = async (targetUrl: string) => {
    const results: PingResult[] = [];
    let clientLatency: number | null = null;

    try {
      // 1. 客户端多方法测试
      setTestStage('执行客户端多方法测试');
      setTestProgress(10);

      const multiTestResult = await advancedTester.performMultipleTests(targetUrl, 5);
      clientLatency = multiTestResult.filteredAverage;

      // 2. WebRTC P2P测试 (如果支持)
      setTestStage('尝试WebRTC P2P测试');
      setTestProgress(20);

      let webrtcResult = null;
      try {
        if (!webrtcTester) {
          setWebrtcTester(new WebRTCLatencyTest());
        }
        webrtcResult = await webrtcTester?.performLatencyTest(targetUrl);
        } catch (error) {
        }

      // 3. 专业监控服务数据
      setTestStage('获取专业监控数据');
      setTestProgress(30);

      let monitoringData = null;
      try {
        monitoringData = await monitoringAggregator.getAggregatedNetworkData(targetUrl);
        } catch (error) {
        }

      // 4. 多云节点测试
      setTestStage('执行多云节点测试');
      setTestProgress(50);

      let cloudResults = [];
      try {
        cloudResults = await multiCloudTester.testAllNodes(targetUrl);
        } catch (error) {
        }

      // 5. 智能预测和校准
      setTestStage('应用智能预测模型');
      setTestProgress(70);

      // 收集用户数据用于模型训练
      if (clientLatency && clientLatency > 0) {
        await dataCollector.collectUserData(targetUrl, clientLatency, {
          province: '未知', // 可以通过IP定位获取
          city: '未知',
          isp: '未知',
          networkType: '未知',
          deviceType: navigator.userAgent.includes('Mobile') ? 'mobile' : 'desktop',
          userAgent: navigator.userAgent,
          ipAddress: '0.0.0.0' // 实际应用中需要获取真实IP
        });
      }

      // 6. 生成最终结果
      setTestStage('生成智能测试结果');
      setTestProgress(90);

      // 合并所有数据源的结果
      const finalResults = await generateIntelligentResults({
        clientLatency,
        webrtcResult,
        monitoringData,
        cloudResults,
        targetUrl
      });

      return finalResults;

    } catch (error) {
      throw error;
    }
  };

  // 生成智能测试结果
  const generateIntelligentResults = async (data: {
    clientLatency: number | null;
    webrtcResult: any;
    monitoringData: any;
    cloudResults: any[];
    targetUrl: string;
  }): Promise<PingResult[]> => {
    const { clientLatency, webrtcResult, monitoringData, cloudResults, targetUrl } = data;

    // 使用completeNodeList作为基础，生成每个省份的结果
    const results: PingResult[] = [];
    const currentHour = new Date().getHours();

    for (const nodeInfo of completeNodeList) {
      let predictedLatency: number;

      // 优先使用多云测试的真实结果
      const cloudResult = cloudResults.find(r =>
        r.node.province === nodeInfo.province && r.status === 'success'
      );

      if (cloudResult) {
        predictedLatency = cloudResult.latency;
      } else if (clientLatency && clientLatency > 0) {
        // 使用机器学习预测
        predictedLatency = latencyPredictor.predictLatency(
          targetUrl,
          nodeInfo.province,
          nodeInfo.name,
          '未知', // ISP
          '未知', // 网络类型
          currentHour
        );

        // 基于客户端延迟进行调整
        const adjustmentFactor = clientLatency / 100; // 基准100ms
        predictedLatency = Math.round(predictedLatency * adjustmentFactor);
      } else {
        // 降级到传统模拟
        predictedLatency = await simulateIntelligentLatency(nodeInfo, targetUrl);
      }

      // 应用WebRTC结果的影响
      if (webrtcResult && webrtcResult.average > 0) {
        const webrtcFactor = webrtcResult.average / (clientLatency || 100);
        predictedLatency = Math.round(predictedLatency * webrtcFactor);
      }

      results.push({
        node: nodeInfo.name,
        ping: Math.max(1, predictedLatency), // 确保最小值为1ms
        status: predictedLatency < 5000 ? 'success' : 'timeout',
        timestamp: Date.now(),
        location: {
          country: 'China',
          city: nodeInfo.name,
          province: nodeInfo.province,
          region: nodeInfo.region || '中国大陆',
          latitude: nodeInfo.latitude,
          longitude: nodeInfo.longitude,
          asn: 0,
          network: 'Intelligent Network Test'
        },
        testMethod: 'Intelligent Multi-Method Test'
      });
    }

    return results;
  };

  // 智能延迟模拟（改进版）
  const simulateIntelligentLatency = async (nodeInfo: any, targetUrl: string): Promise<number> => {
    // 使用机器学习预测器的默认方法
    return latencyPredictor.predictLatency(
      targetUrl,
      nodeInfo.province,
      nodeInfo.name,
      '未知',
      '未知',
      new Date().getHours()
    );
  };

  // 客户端延迟测试函数
  const performClientLatencyTest = async (targetUrl: string): Promise<number | null> => {
    try {
      // 确保URL格式正确
      let testUrl = targetUrl;
      if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
        testUrl = 'https://' + testUrl;
      }

      // 执行HTTP HEAD请求测试
      const startTime = performance.now();

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      try {
        const response = await fetch(testUrl, {
          method: 'HEAD',
          mode: 'no-cors', // 避免CORS问题
          signal: controller.signal,
          cache: 'no-cache'
        });

        clearTimeout(timeoutId);
        const endTime = performance.now();
        const latency = Math.round(endTime - startTime);

        return latency;

      } catch (fetchError) {
        clearTimeout(timeoutId);
        // 备用方法：使用Image对象测试（适用于某些网站）
        return await performImageLatencyTest(testUrl);
      }

    } catch (error) {
      return null;
    }
  };

  // 备用的图片延迟测试
  const performImageLatencyTest = async (targetUrl: string): Promise<number | null> => {
    return new Promise((resolve) => {
      const startTime = performance.now();
      const img = new Image();

      const cleanup = () => {
        img.onload = null;
        img.onerror = null;
      };

      img.onload = () => {
        cleanup();
        const endTime = performance.now();
        const latency = Math.round(endTime - startTime);
        resolve(latency);
      };

      img.onerror = () => {
        cleanup();
        resolve(null);
      };

      // 设置超时
      setTimeout(() => {
        cleanup();
        resolve(null);
      }, 8000);

      // 尝试加载favicon或其他小资源
      try {
        const url = new URL(targetUrl);
        img.src = `${url.protocol}//${url.host}/favicon.ico?t=${Date.now()}`;
      } catch {
        resolve(null);
      }
    });
  };

  // 增强的测试启动函数
  const startPing = async () => {
    if (!target.trim()) return;
    setIsRunning(true);
    setPingResults([]);
    setTestProgress(0);
    setTestStage('准备测试');

    try {
      // 检查缓存 - 优先使用混合测试缓存
      let cachedResult = networkTestCache.getCachedTestResult<PingResult[]>(target.trim(), 'hybrid-accurate');

      if (!cachedResult) {
        // 如果没有混合测试缓存，检查其他缓存
        cachedResult = networkTestCache.getCachedTestResult<PingResult[]>(target.trim(), 'backend') ||
                      networkTestCache.getCachedTestResult<PingResult[]>(target.trim(), 'intelligent');
      }

      if (cachedResult && !useEnhancedTesting) {
        setPingResults(cachedResult);
        setTestProgress(100);
        setTestStage('使用缓存结果');
        return;
      }

      // 🚀 使用新的准确算法 - 客户端混合测试 + 后端智能算法
      setTestStage('执行混合测试');
      setTestProgress(20);

      // 执行客户端混合测试
      let hybridResult = null;
      try {
        setTestStage('客户端延迟测试');
        setTestProgress(30);

        const clientLatency = await performClientLatencyTest(target.trim());
        hybridResult = {
          httpLatency: clientLatency,
          wsLatency: null,
          resourceLatency: null,
          averageLatency: clientLatency,
          testMethod: 'HTTP HEAD Request'
        };

        setTestProgress(40);
      } catch (error) {
        }

      // 调用后端API，传递混合测试结果
      setTestStage('调用后端API');
      setTestProgress(50);

      const response = await fetch('/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: target.trim(),
          maxNodes: useBatchTesting ? 25 : 35,
          useBatchTesting: useBatchTesting,
          useHybridTest: hybridResult !== null,
          hybridResult: hybridResult
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setTestProgress(70);

      if (data.success && data.results) {
        const results = data.results.map((result: any) => ({
          node: result.node,
          ping: result.ping,
          status: result.status,
          timestamp: result.timestamp,
          location: result.location,
          testMethod: result.testMethod || 'Backend API',
          testEndpoint: result.testEndpoint
        }));

        // 保存到历史记录
        results.forEach((result: PingResult) => {
          historyStorage.saveRecord({
            target: target.trim(),
            timestamp: result.timestamp,
            latency: result.ping,
            jitter: 0,
            packetLoss: result.status === 'timeout' ? 100 : 0,
            bandwidth: 0,
            downloadSpeed: 0,
            uploadSpeed: 0,
            mtu: 1500,
            status: result.status === 'error' ? 'failed' : result.status,
            testMethod: result.testMethod || 'Backend API',
            reliability: 50,
            location: result.location
          });
        });

        // 缓存结果
        const cacheKey = hybridResult ? 'hybrid-accurate' : 'backend';
        networkTestCache.smartCache(target.trim(), cacheKey, results);

        setPingResults(results.sort((a: PingResult, b: PingResult) => a.ping - b.ping));
        updateMapColors(results);
        setTestProgress(100);
        setTestStage(hybridResult ? '混合测试完成' : '后端测试完成');
      } else {
        throw new Error(data.error || 'Backend API test failed');
      }
    } catch (error) {
      setTestStage('降级到模拟测试');
      setTestProgress(80);

      // 降级到模拟数据
      await performFallbackTest();
    } finally {
      setIsRunning(false);
      setTimeout(() => {
        setTestProgress(0);
        setTestStage('');
      }, 2000);
    }
  };

  // 降级测试函数
  const performFallbackTest = async () => {
    const completeNodeList = [
      // 直辖市
      { name: '北京', province: '北京' },
      { name: '上海', province: '上海' },
      { name: '天津', province: '天津' },
      { name: '重庆', province: '重庆' },
      // 省会城市
      { name: '石家庄', province: '河北' },
      { name: '太原', province: '山西' },
      { name: '呼和浩特', province: '内蒙古' },
      { name: '沈阳', province: '辽宁' },
      { name: '长春', province: '吉林' },
      { name: '哈尔滨', province: '黑龙江' },
      { name: '南京', province: '江苏' },
      { name: '杭州', province: '浙江' },
      { name: '合肥', province: '安徽' },
      { name: '福州', province: '福建' },
      { name: '南昌', province: '江西' },
      { name: '济南', province: '山东' },
      { name: '郑州', province: '河南' },
      { name: '武汉', province: '湖北' },
      { name: '长沙', province: '湖南' },
      { name: '广州', province: '广东' },
      { name: '海口', province: '海南' },
      { name: '南宁', province: '广西' },
      { name: '成都', province: '四川' },
      { name: '贵阳', province: '贵州' },
      { name: '昆明', province: '云南' },
      { name: '拉萨', province: '西藏' },
      { name: '西安', province: '陕西' },
      { name: '兰州', province: '甘肃' },
      { name: '西宁', province: '青海' },
      { name: '银川', province: '宁夏' },
      { name: '乌鲁木齐', province: '新疆' },
      // 港澳台
      { name: '香港', province: '香港' },
      { name: '澳门', province: '澳门' },
      { name: '台北', province: '台湾' }
    ];

    if (useBatchTesting) {
      // 使用分批测试
      const testNodes = completeNodeList.map(node => ({
        ...node,
        priority: ['北京', '上海', '广州', '深圳'].includes(node.name) ? 1 : 2,
        region: '模拟',
        endpoints: [target]
      }));

      const results = await batchTestManager.executeBatchTests(
        testNodes,
        async (node) => simulatePing(node, target),
        batchTestManager.adaptiveConfig(testNodes),
        (progress, stage, completed, total) => {
          setTestProgress(80 + (progress * 0.2));
          setTestStage(`${stage} (${completed}/${total})`);
        }
      );

      setPingResults(results.sort((a: PingResult, b: PingResult) => a.ping - b.ping));
    } else {
      // 传统并发测试
      const promises = completeNodeList.map(nodeInfo => simulatePing(nodeInfo, target));
      const results = await Promise.all(promises);
      setPingResults(results.sort((a: PingResult, b: PingResult) => a.ping - b.ping));
    }

    setTestProgress(100);
    setTestStage('降级测试完成');
    updateMapColors(pingResults);
  };

  // WebRTC测试结果处理
  const handleWebRTCResult = (result: any) => {
    setEnhancedResults(prev => prev ? { ...prev, ...result } : result);
  };

  // 增强测试结果处理
  const handleEnhancedResult = (result: EnhancedTestResult) => {
    setEnhancedResults(result);

    // 保存到历史记录
    historyStorage.saveRecord({
      target: target.trim(),
      timestamp: result.timestamp,
      latency: result.latency,
      jitter: result.jitter,
      packetLoss: result.packetLoss,
      bandwidth: result.bandwidth,
      downloadSpeed: result.downloadSpeed,
      uploadSpeed: result.uploadSpeed,
      mtu: result.mtu,
      status: result.status,
      testMethod: result.testMethod,
      reliability: result.reliability
    });
  };

  // 网络指标更新处理
  const handleMetricsUpdate = (metrics: NetworkMetrics) => {
    setNetworkMetrics(metrics);

    // 保存到历史记录
    historyStorage.saveRecord({
      target: target.trim(),
      timestamp: metrics.timestamp,
      latency: metrics.latency,
      jitter: metrics.jitter,
      packetLoss: metrics.packetLoss,
      bandwidth: metrics.bandwidth,
      downloadSpeed: metrics.downloadSpeed,
      uploadSpeed: metrics.uploadSpeed,
      mtu: metrics.mtu,
      status: 'success',
      testMethod: 'Extended Metrics',
      reliability: metrics.stability
    });
  };

  const stopPing = () => {
    setIsRunning(false);
    setTestProgress(0);
    setTestStage('');
  };

  // 更新显示（现在不需要地图更新）
  const updateMapColors = (results: PingResult[]) => {
    // 省份网格会自动根据pingResults更新，不需要额外操作
    };

  useEffect(() => {
    }, []);

  return (
    <div className={`rounded-lg shadow-sm p-8 mb-8 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
      {/* 头部区域 - 包含标题和计数器 */}
      <div className="relative flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
        {/* 左侧标题区域 */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center shadow-lg">
            <span className="text-white text-lg font-bold">P</span>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3">
            <h3 className={`text-2xl font-semibold transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Ping 工具
            </h3>
            <span className={`text-lg transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} hidden sm:inline`}>
              - 网络连通性测试
            </span>
          </div>
        </div>

        {/* 右侧计数器 */}
        <div className="flex justify-end sm:justify-start">
          <VisitCounter isDarkMode={isDarkMode} />
        </div>
      </div>

      {/* 输入控制区域 */}
      <div className="mb-6">
        <div className="flex space-x-3 mb-4">
          <input
            type="text"
            value={target}
            onChange={(e) => setTarget(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && target.trim() && !isRunning) {
                startPing();
              }
            }}
            placeholder="请输入域名，例如：example.com，8.8.8.8 (按 Enter 开始)"
            className={`flex-1 px-4 py-3 text-lg border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            }`}
            disabled={isRunning}
          />
          <button
            onClick={isRunning ? stopPing : startPing}
            disabled={!target.trim()}
            className={`px-6 py-3 text-lg rounded-lg font-medium transition-colors duration-300 ${
              isRunning
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : target.trim()
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isRunning ? '停止' : '开始'}
          </button>
        </div>

        {/* 视图切换按钮 */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setViewMode('map')}
            className={`px-3 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'map'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            🗺️ 地图
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`px-3 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'grid'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📊 网格
          </button>
          <button
            onClick={() => setViewMode('monitor')}
            className={`px-3 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'monitor'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📊 监控
          </button>
          <button
            onClick={() => setViewMode('routing')}
            className={`px-3 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'routing'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            🧠 路由
          </button>
          <button
            onClick={() => setViewMode('metrics')}
            className={`px-3 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'metrics'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📈 指标
          </button>
        </div>

        {/* 测试选项 */}
        <div className="flex flex-wrap gap-3 mt-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={useEnhancedTesting}
              onChange={(e) => setUseEnhancedTesting(e.target.checked)}
              className="rounded"
            />
            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              增强测试
            </span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={useBatchTesting}
              onChange={(e) => setUseBatchTesting(e.target.checked)}
              className="rounded"
            />
            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              分批测试
            </span>
          </label>

          <button
            onClick={() => performRealDataTest()}
            disabled={isRunning || !target.trim()}
            className={`px-3 py-1 text-sm rounded-lg font-medium transition-colors duration-300 ${
              isRunning || !target.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-red-600 hover:bg-red-700 text-white'
            }`}
          >
            🌐 真实数据
          </button>
        </div>

        {/* 进度条 */}
        {isRunning && (
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {testStage}
              </span>
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {Math.round(testProgress)}%
              </span>
            </div>
            <div className={`w-full bg-gray-200 rounded-full h-2 ${isDarkMode ? 'bg-gray-700' : ''}`}>
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${testProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* 增强测试工具 */}
        {useEnhancedTesting && target && (
          <div className="flex flex-wrap gap-2 mt-3">
            <WebRTCTester
              target={target}
              onResult={handleWebRTCResult}
            />
            <EnhancedNetworkTester
              target={target}
              onResult={handleEnhancedResult}
              onProgress={(progress, stage) => {
                if (isRunning) {
                  setTestProgress(Math.min(progress, 90));
                  setTestStage(stage);
                }
              }}
            />
          </div>
        )}
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：视图区域 - 占2/3宽度 */}
        <div className="lg:col-span-2">
          {viewMode === 'map' ? (
            /* 地图视图 */
            <ChinaMap
              pingResults={pingResults}
              isDarkMode={isDarkMode}
              onProvinceClick={(provinceName) => {
                }}
            />
          ) : viewMode === 'monitor' ? (
            /* 性能监控视图 */
            <PerformanceMonitor
              target={target}
              isDarkMode={isDarkMode}
            />
          ) : viewMode === 'routing' ? (
            /* 智能路由视图 */
            <SmartRouting
              target={target}
              currentLatency={enhancedResults?.latency || networkMetrics?.latency || 0}
              isDarkMode={isDarkMode}
            />
          ) : viewMode === 'metrics' ? (
            /* 扩展指标视图 */
            <ExtendedMetrics
              target={target}
              isDarkMode={isDarkMode}
              onMetricsUpdate={handleMetricsUpdate}
            />
          ) : (
            /* 网格视图 */
            <div className={`relative rounded-lg overflow-hidden transition-colors duration-300 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`} style={{ height: '600px' }}>
              <div className="p-4 h-full flex flex-col">
                <h4 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  中国网络延迟网格
                </h4>

                <div className="flex-1 grid grid-cols-6 gap-3 p-4 max-h-full overflow-y-auto">
                  {testNodes.map((nodeInfo, index) => {
                    const result = pingResults.find(r => r.node === nodeInfo.name);
                    const bgColor = result ? getPingBgClass(result.ping, result.status) : (isDarkMode ? 'bg-gray-600' : 'bg-gray-200');

                    return (
                      <div
                        key={index}
                        className={`${bgColor} rounded-lg p-3 text-center transition-all duration-300 hover:scale-105 shadow-lg`}
                        style={{
                          minHeight: '80px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}
                      >
                        <div className="text-sm font-bold text-white mb-1">
                          {nodeInfo.name}
                        </div>
                        <div className="text-xs text-white font-medium">
                          {result ? (
                            result.status === 'success' ? `${result.ping}ms` : '超时'
                          ) : '未测试'}
                        </div>
                        {result && result.testMethod && (
                          <div className="text-xs text-white opacity-75 mt-1">
                            {result.testMethod === 'CloudPing HTTP HEAD' ? 'CloudPing' :
                             result.testMethod === 'CloudPing Fallback' ? 'Fallback' :
                             result.testMethod === 'Direct HTTP HEAD' ? 'Direct' : 'Sim'}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 颜色图例 */}
              <div className="p-4 border-t" style={{ height: '100px' }}>
                <h5 className={`text-sm font-bold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  延迟等级
                </h5>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#16a34a' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>≤50ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#22c55e' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>51ms-100ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#84cc16' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>101ms-200ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#eab308' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>201ms-250ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#ea580c' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>&gt;250ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#dc2626' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>超时</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 右侧：数据表格和增强信息 - 占1/3宽度 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 增强测试结果 */}
          {enhancedResults && (
            <div className={`rounded-lg p-4 transition-colors duration-300 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <h4 className={`text-lg font-semibold mb-3 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                ⚡ 增强测试结果
              </h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className={`block font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>延迟</span>
                  <span className={`text-lg font-bold ${enhancedResults.latency <= 100 ? 'text-green-600' : enhancedResults.latency <= 300 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {enhancedResults.latency}ms
                  </span>
                </div>
                <div>
                  <span className={`block font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>抖动</span>
                  <span className={`text-lg font-bold ${enhancedResults.jitter <= 10 ? 'text-green-600' : enhancedResults.jitter <= 30 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {enhancedResults.jitter}ms
                  </span>
                </div>
                <div>
                  <span className={`block font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>丢包率</span>
                  <span className={`text-lg font-bold ${enhancedResults.packetLoss <= 1 ? 'text-green-600' : enhancedResults.packetLoss <= 5 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {enhancedResults.packetLoss}%
                  </span>
                </div>
                <div>
                  <span className={`block font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>带宽</span>
                  <span className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {enhancedResults.bandwidth}Kbps
                  </span>
                </div>
                <div className="col-span-2">
                  <span className={`block font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>可靠性</span>
                  <div className="flex items-center space-x-2">
                    <div className={`flex-1 bg-gray-200 rounded-full h-2 ${isDarkMode ? 'bg-gray-600' : ''}`}>
                      <div
                        className={`h-2 rounded-full ${enhancedResults.reliability >= 80 ? 'bg-green-600' : enhancedResults.reliability >= 60 ? 'bg-yellow-600' : 'bg-red-600'}`}
                        style={{ width: `${enhancedResults.reliability}%` }}
                      ></div>
                    </div>
                    <span className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {enhancedResults.reliability}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 测试结果列表 */}
          <div className={`rounded-lg p-4 transition-colors duration-300 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`} style={{ height: enhancedResults ? '400px' : '600px', display: 'flex', flexDirection: 'column' }}>
            <h4 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              📊 节点测试结果
            </h4>
            <div className="flex-1 overflow-y-auto space-y-2 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200">
              <div className={`grid grid-cols-3 gap-2 font-medium text-xs pb-2 border-b border-gray-300 sticky top-0 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>节点</span>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>延迟</span>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>状态</span>
              </div>

              {isRunning && pingResults.length === 0 && (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>正在测试中...</p>
                  </div>
                </div>
              )}

              {pingResults.length > 0 ? (
                pingResults.map((result, index) => (
                  <div key={index} className={`grid grid-cols-3 gap-2 py-1 px-2 rounded-md transition-all duration-300 hover:scale-105 cursor-pointer ${isDarkMode ? 'hover:bg-gray-600' : 'hover:bg-gray-200'}`}>
                    <span className={`text-xs font-medium transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {result.node}
                    </span>
                    <span className="text-xs font-semibold" style={{
                      color: getPingColor(result.ping, result.status)
                    }}>
                      {result.status === 'success' ? `${result.ping}ms` : '超时'}
                    </span>
                    <span className="text-xs font-medium" style={{
                      color: getPingColor(result.ping, result.status)
                    }}>
                      {result.status === 'success' ? '✓' : '✗'}
                    </span>
                  </div>
                ))
              ) : !isRunning && (
                <div className="flex items-center justify-center h-full">
                  <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    请输入域名或IP地址开始测试
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PingTool;
