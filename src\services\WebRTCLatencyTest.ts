// WebRTC P2P 延迟测试服务
export class WebRTCLatencyTest {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private measurements: number[] = [];

  constructor() {
    this.initializePeerConnection();
  }

  private initializePeerConnection() {
    this.peerConnection = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun.qq.com:3478' }, // 腾讯STUN服务器
        { urls: 'stun:stun.miwifi.com:3478' } // 小米STUN服务器
      ]
    });

    // 创建数据通道
    this.dataChannel = this.peerConnection.createDataChannel('latency-test', {
      ordered: true
    });

    this.setupDataChannelHandlers();
  }

  private setupDataChannelHandlers() {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log('WebRTC数据通道已打开');
      this.startLatencyMeasurement();
    };

    this.dataChannel.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'ping-response') {
        const latency = Date.now() - data.timestamp;
        this.measurements.push(latency);
        console.log(`WebRTC延迟: ${latency}ms`);
      }
    };
  }

  // 开始延迟测量 - 优化版本
  private startLatencyMeasurement() {
    const measureCount = 3; // 从10次减少到3次
    let currentMeasure = 0;

    const sendPing = () => {
      if (currentMeasure >= measureCount || !this.dataChannel) return;

      const pingData = {
        type: 'ping',
        timestamp: Date.now(),
        sequence: currentMeasure
      };

      this.dataChannel.send(JSON.stringify(pingData));
      currentMeasure++;

      setTimeout(sendPing, 300); // 从1秒减少到300ms
    };

    sendPing();
  }

  // 执行WebRTC延迟测试 - 优化版本，减少等待时间
  async performLatencyTest(targetDomain: string): Promise<{
    average: number;
    min: number;
    max: number;
    jitter: number;
    packetLoss: number;
  }> {
    return new Promise((resolve, reject) => {
      // 设置更短的超时时间
      const timeout = setTimeout(() => {
        reject(new Error('WebRTC测试超时'));
      }, 8000); // 从30秒减少到8秒

      // 模拟WebRTC连接建立过程
      this.establishConnection(targetDomain)
        .then(() => {
          // 等待测量完成，减少所需测量次数
          const checkMeasurements = () => {
            if (this.measurements.length >= 3) { // 从10次减少到3次
              clearTimeout(timeout);
              const result = this.calculateStatistics();
              resolve(result);
            } else {
              setTimeout(checkMeasurements, 300); // 从1秒减少到300ms
            }
          };
          checkMeasurements();
        })
        .catch(reject);
    });
  }

  private async establishConnection(targetDomain: string): Promise<void> {
    // 简化的WebRTC连接建立过程
    // 实际实现需要信令服务器
    console.log(`尝试建立WebRTC连接到: ${targetDomain}`);

    // 减少模拟连接建立延迟
    await new Promise(resolve => setTimeout(resolve, 500)); // 从2秒减少到500ms

    // 模拟成功建立连接
    if (this.dataChannel) {
      this.dataChannel.onopen?.({} as Event);
    }

    // 开始延迟测量
    this.startLatencyMeasurement();
  }

  private calculateStatistics() {
    if (this.measurements.length === 0) {
      return { average: 0, min: 0, max: 0, jitter: 0, packetLoss: 0 };
    }

    const sorted = [...this.measurements].sort((a, b) => a - b);
    const average = this.measurements.reduce((a, b) => a + b, 0) / this.measurements.length;
    const min = sorted[0];
    const max = sorted[sorted.length - 1];

    // 计算抖动 (Jitter)
    let jitterSum = 0;
    for (let i = 1; i < this.measurements.length; i++) {
      jitterSum += Math.abs(this.measurements[i] - this.measurements[i - 1]);
    }
    const jitter = jitterSum / (this.measurements.length - 1);

    // 计算丢包率 (简化版)
    const expectedPackets = 10;
    const receivedPackets = this.measurements.length;
    const packetLoss = ((expectedPackets - receivedPackets) / expectedPackets) * 100;

    return {
      average: Math.round(average),
      min: Math.round(min),
      max: Math.round(max),
      jitter: Math.round(jitter),
      packetLoss: Math.round(packetLoss * 100) / 100
    };
  }

  // 清理资源
  cleanup() {
    if (this.dataChannel) {
      this.dataChannel.close();
    }
    if (this.peerConnection) {
      this.peerConnection.close();
    }
    this.measurements = [];
  }
}

// 高精度网络测试服务
export class AdvancedNetworkTester {
  // 多次测试取平均值
  async performMultipleTests(url: string, testCount: number = 10): Promise<{
    results: number[];
    average: number;
    median: number;
    standardDeviation: number;
    filteredAverage: number; // 排除异常值后的平均值
  }> {
    const results: number[] = [];
    
    for (let i = 0; i < testCount; i++) {
      try {
        const latency = await this.singleLatencyTest(url);
        results.push(latency);
        
        // 减少测试间隔，提高响应速度
        await new Promise(resolve => setTimeout(resolve, 100)); // 从500ms减少到100ms
      } catch (error) {
        console.warn(`测试 ${i + 1} 失败:`, error);
      }
    }

    return this.analyzeResults(results);
  }

  private async singleLatencyTest(url: string): Promise<number> {
    const start = performance.now();
    
    try {
      await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache',
        signal: AbortSignal.timeout(3000) // 从10秒减少到3秒
      });
      
      return performance.now() - start;
    } catch (error) {
      throw new Error(`网络测试失败: ${error}`);
    }
  }

  private analyzeResults(results: number[]) {
    if (results.length === 0) {
      return {
        results: [],
        average: 0,
        median: 0,
        standardDeviation: 0,
        filteredAverage: 0
      };
    }

    const sorted = [...results].sort((a, b) => a - b);
    const average = results.reduce((a, b) => a + b, 0) / results.length;
    const median = sorted[Math.floor(sorted.length / 2)];

    // 计算标准差
    const variance = results.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / results.length;
    const standardDeviation = Math.sqrt(variance);

    // 排除异常值 (超出2个标准差的值)
    const filtered = results.filter(val => 
      Math.abs(val - average) <= 2 * standardDeviation
    );
    const filteredAverage = filtered.length > 0 
      ? filtered.reduce((a, b) => a + b, 0) / filtered.length 
      : average;

    return {
      results,
      average: Math.round(average),
      median: Math.round(median),
      standardDeviation: Math.round(standardDeviation),
      filteredAverage: Math.round(filteredAverage)
    };
  }

  // DNS解析时间测试
  async measureDNSResolution(domain: string): Promise<number> {
    const start = performance.now();
    
    try {
      // 使用DNS over HTTPS进行解析
      const response = await fetch(`https://1.1.1.1/dns-query?name=${domain}&type=A`, {
        headers: {
          'Accept': 'application/dns-json'
        }
      });
      
      await response.json();
      return performance.now() - start;
    } catch (error) {
      console.warn('DNS解析测试失败:', error);
      return 0;
    }
  }

  // TCP连接时间测试 (使用WebSocket模拟)
  async measureTCPConnection(url: string): Promise<number> {
    return new Promise((resolve) => {
      const start = performance.now();
      const wsUrl = url.replace(/^https?:\/\//, 'wss://').replace(/^http:\/\//, 'ws://');
      
      const ws = new WebSocket(wsUrl);
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve(0); // 连接失败
      }, 3000); // 从10秒减少到3秒
      
      ws.onopen = () => {
        clearTimeout(timeout);
        const connectionTime = performance.now() - start;
        ws.close();
        resolve(connectionTime);
      };
      
      ws.onerror = () => {
        clearTimeout(timeout);
        resolve(0);
      };
    });
  }
}
