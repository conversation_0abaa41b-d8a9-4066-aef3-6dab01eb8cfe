import { NextRequest, NextResponse } from 'next/server';
import { aliyunMonitor } from '@/services/AliyunMonitorIntegration';
import { performMultiCloudPing } from '@/services/PingService';
// import { ValidationUtils } from '@/utils/ValidationUtils';

export async function POST(request: NextRequest) {
  try {
    console.log('📥 收到增强ping测试请求');

    // 1. 验证输入
    const body = await request.json();
    const { target } = body;

    console.log('📋 请求数据:', { target });

    // 简单验证
    if (!target || typeof target !== 'string') {
      console.log('❌ 输入验证失败:', { target });
      return NextResponse.json(
        { error: '请提供有效的目标URL' },
        { status: 400 }
      );
    }

    console.log(`🌟 开始增强ping测试: ${target}`);

    // 2. 执行实时ping测试 - 直接调用共享服务
    console.log('🔍 开始多节点ping测试...');
    const realTimeResults = await performMultiCloudPing(target);

    // 3. 尝试获取阿里云监控历史数据
    let historicalData = null;
    let monitoringEnabled = false;

    if (aliyunMonitor.isConfigured()) {
      try {
        console.log('📊 获取阿里云监控数据...');
        const stats = await aliyunMonitor.getWebsiteStats(target);
        
        if (stats.success) {
          historicalData = stats.data;
          monitoringEnabled = true;
          console.log('✅ 阿里云监控数据获取成功');
        } else {
          console.warn('⚠️ 阿里云监控数据获取失败:', stats.error);
        }
      } catch (error) {
        console.warn('⚠️ 阿里云监控API调用失败:', error);
      }
    } else {
      console.log('ℹ️ 阿里云监控未配置，跳过历史数据获取');
    }

    // 4. 生成智能建议
    const recommendations = generateRecommendations(realTimeResults, historicalData);

    // 5. 返回增强结果
    const enhancedResult = {
      success: true,
      timestamp: new Date().toISOString(),
      target,
      realTime: realTimeResults,
      historical: historicalData,
      recommendations,
      features: {
        monitoringEnabled,
        historicalDataAvailable: !!historicalData,
        recommendationsGenerated: recommendations.length > 0
      }
    };

    console.log(`✅ 增强ping测试完成: ${target}`);
    return NextResponse.json(enhancedResult);

  } catch (error) {
    console.error('❌ 增强ping测试失败:', error);
    console.error('错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');

    // 更详细的错误信息
    let errorMessage = '增强ping测试失败';
    let errorDetails = '';

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || '';
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : undefined,
        timestamp: new Date().toISOString(),
        service: 'enhanced-ping'
      },
      { status: 500 }
    );
  }
}

// 生成智能建议
function generateRecommendations(realTimeData: any, historicalData: any): string[] {
  const recommendations: string[] = [];

  if (!realTimeData?.success) {
    recommendations.push('⚠️ 当前网站无法访问，建议检查网站状态');
    return recommendations;
  }

  // 基于实时数据的建议
  const results = realTimeData.results || [];
  const avgLatency = results.reduce((sum: number, r: any) => sum + (r.ping || 0), 0) / results.length;

  if (avgLatency > 1000) {
    recommendations.push('🐌 当前延迟较高(>1000ms)，可能存在网络拥堵');
  } else if (avgLatency > 500) {
    recommendations.push('⚠️ 延迟偏高(>500ms)，建议关注网络状况');
  } else if (avgLatency < 100) {
    recommendations.push('🚀 网络状况良好，延迟较低');
  }

  // 基于历史数据的建议
  if (historicalData) {
    const { availability, avgResponseTime, regionalData } = historicalData;

    if (availability < 95) {
      recommendations.push(`📉 历史可用率较低(${availability}%)，建议加强监控`);
    } else if (availability > 99) {
      recommendations.push(`✅ 历史可用率优秀(${availability}%)，服务稳定`);
    }

    if (avgResponseTime > avgLatency * 1.5) {
      recommendations.push('📈 当前延迟比历史平均值高50%以上，可能存在临时问题');
    } else if (avgResponseTime < avgLatency * 0.7) {
      recommendations.push('📉 当前延迟比历史平均值低30%以上，网络状况较好');
    }

    // 地域建议
    if (regionalData && regionalData.length > 0) {
      const bestRegion = regionalData.reduce((best: any, current: any) => 
        current.avgLatency < best.avgLatency ? current : best
      );
      const worstRegion = regionalData.reduce((worst: any, current: any) => 
        current.avgLatency > worst.avgLatency ? current : worst
      );

      if (bestRegion.avgLatency < worstRegion.avgLatency * 0.7) {
        recommendations.push(`🌍 ${bestRegion.region}地区访问速度最佳(${bestRegion.avgLatency}ms)`);
      }
    }
  }

  // 通用建议
  const timeOfDay = new Date().getHours();
  if (timeOfDay >= 19 && timeOfDay <= 21) {
    recommendations.push('🕐 当前为网络高峰期(19-21点)，延迟可能偏高');
  } else if (timeOfDay >= 2 && timeOfDay <= 6) {
    recommendations.push('🌙 当前为网络低峰期(2-6点)，延迟通常较低');
  }

  return recommendations;
}

// 支持GET请求查看API状态
export async function GET() {
  return NextResponse.json({
    service: 'Enhanced Ping API',
    version: '1.0.0',
    features: {
      realTimePing: true,
      historicalData: aliyunMonitor.isConfigured(),
      smartRecommendations: true
    },
    status: 'active',
    timestamp: new Date().toISOString()
  });
}
