import { NextRequest, NextResponse } from 'next/server';

// 免费云服务节点配置
const FREE_CLOUD_NODES = {
  // Vercel Edge Functions - 全球节点
  vercel: [
    { name: '香港', endpoint: 'https://your-app.vercel.app/api/ping-vercel-edge', region: 'hkg1', provider: 'vercel' },
    { name: '新加坡', endpoint: 'https://your-app.vercel.app/api/ping-vercel-edge', region: 'sin1', provider: 'vercel' },
    { name: '东京', endpoint: 'https://your-app.vercel.app/api/ping-vercel-edge', region: 'nrt1', provider: 'vercel' },
    { name: '首尔', endpoint: 'https://your-app.vercel.app/api/ping-vercel-edge', region: 'icn1', provider: 'vercel' },
    { name: '旧金山', endpoint: 'https://your-app.vercel.app/api/ping-vercel-edge', region: 'sfo1', provider: 'vercel' }
  ],
  
  // Cloudflare Workers - 全球200+节点
  cloudflare: [
    { name: '北京', endpoint: 'https://ping-worker.your-domain.workers.dev', region: 'PEK', provider: 'cloudflare' },
    { name: '上海', endpoint: 'https://ping-worker.your-domain.workers.dev', region: 'SHA', provider: 'cloudflare' },
    { name: '广州', endpoint: 'https://ping-worker.your-domain.workers.dev', region: 'CAN', provider: 'cloudflare' },
    { name: '深圳', endpoint: 'https://ping-worker.your-domain.workers.dev', region: 'SZX', provider: 'cloudflare' },
    { name: '香港', endpoint: 'https://ping-worker.your-domain.workers.dev', region: 'HKG', provider: 'cloudflare' }
  ],
  
  // Railway 部署的节点
  railway: [
    { name: '美国东部', endpoint: 'https://your-app.railway.app/ping', region: 'us-east', provider: 'railway' },
    { name: '美国西部', endpoint: 'https://your-app.railway.app/ping', region: 'us-west', provider: 'railway' }
  ],
  
  // Fly.io 全球分布式节点
  flyio: [
    { name: '东京', endpoint: 'https://your-app.fly.dev/ping', region: 'nrt', provider: 'flyio' },
    { name: '新加坡', endpoint: 'https://your-app.fly.dev/ping', region: 'sin', provider: 'flyio' },
    { name: '悉尼', endpoint: 'https://your-app.fly.dev/ping', region: 'syd', provider: 'flyio' }
  ]
};

// 调用免费云节点进行测试
async function callFreeCloudNode(node: any, target: string, timeout: number = 8000): Promise<any> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const startTime = Date.now();
    const response = await fetch(node.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Free-Cloud-Ping-Aggregator/1.0'
      },
      body: JSON.stringify({ 
        target,
        testCount: 2 // 减少测试次数以提高速度
      }),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    const requestTime = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    return {
      success: true,
      name: node.name,
      provider: node.provider,
      region: node.region,
      latency: data.averageLatency || data.latency || null,
      minLatency: data.minLatency || null,
      maxLatency: data.maxLatency || null,
      successRate: data.successRate || 100,
      requestTime,
      testMethod: data.testMethod || `${node.provider}-ping`,
      location: data.location || {},
      timestamp: new Date().toISOString(),
      rawData: data
    };
    
  } catch (error) {
    return {
      success: false,
      name: node.name,
      provider: node.provider,
      region: node.region,
      error: error.name === 'AbortError' ? 'Timeout' : error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// 并发调用所有免费云节点
async function callAllFreeNodes(target: string): Promise<any[]> {
  const allNodes = [
    ...FREE_CLOUD_NODES.vercel,
    ...FREE_CLOUD_NODES.cloudflare,
    ...FREE_CLOUD_NODES.railway,
    ...FREE_CLOUD_NODES.flyio
  ];
  
  // 并发调用所有节点，设置较短的超时时间
  const promises = allNodes.map(node => callFreeCloudNode(node, target, 6000));
  const results = await Promise.allSettled(promises);
  
  return results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return {
        success: false,
        name: allNodes[index].name,
        provider: allNodes[index].provider,
        region: allNodes[index].region,
        error: 'Promise rejected: ' + result.reason?.message,
        timestamp: new Date().toISOString()
      };
    }
  });
}

// 生成智能模拟数据作为备用
function generateBackupData(target: string): any[] {
  const targetLower = target.toLowerCase();
  const isChineseSite = targetLower.includes('baidu') || targetLower.includes('qq') || 
                       targetLower.includes('taobao') || targetLower.includes('163') ||
                       targetLower.includes('sina') || targetLower.includes('weibo');
  
  const chineseCities = [
    { name: '北京', baseLatency: isChineseSite ? 8 : 180 },
    { name: '上海', baseLatency: isChineseSite ? 12 : 190 },
    { name: '广州', baseLatency: isChineseSite ? 15 : 200 },
    { name: '深圳', baseLatency: isChineseSite ? 18 : 210 },
    { name: '杭州', baseLatency: isChineseSite ? 14 : 195 },
    { name: '成都', baseLatency: isChineseSite ? 22 : 220 },
    { name: '武汉', baseLatency: isChineseSite ? 18 : 205 },
    { name: '西安', baseLatency: isChineseSite ? 25 : 230 }
  ];
  
  return chineseCities.map(city => ({
    success: true,
    name: city.name,
    provider: 'intelligent-simulation',
    region: `cn-${city.name.toLowerCase()}`,
    latency: Math.round(city.baseLatency + Math.random() * 10),
    testMethod: 'Intelligent-Algorithm-Simulation',
    timestamp: new Date().toISOString(),
    isSimulated: true
  }));
}

export async function POST(request: NextRequest) {
  try {
    const { target } = await request.json();
    
    if (!target) {
      return NextResponse.json(
        { error: 'Target URL is required' },
        { status: 400 }
      );
    }
    
    console.log(`🌐 开始免费云节点ping测试: ${target}`);
    
    // 并发调用所有免费云节点
    const freeNodeResults = await callAllFreeNodes(target);
    
    // 过滤成功的结果
    const successfulResults = freeNodeResults.filter(result => result.success);
    
    // 如果免费节点结果太少，添加智能模拟数据
    let finalResults = [...successfulResults];
    if (successfulResults.length < 5) {
      console.log(`⚠️ 免费节点结果不足(${successfulResults.length}个)，添加智能模拟数据`);
      const backupData = generateBackupData(target);
      finalResults = [...successfulResults, ...backupData];
    }
    
    // 计算统计信息
    const realResults = finalResults.filter(r => !r.isSimulated);
    const simulatedResults = finalResults.filter(r => r.isSimulated);
    
    const response = {
      success: true,
      target,
      timestamp: new Date().toISOString(),
      summary: {
        totalNodes: finalResults.length,
        realNodes: realResults.length,
        simulatedNodes: simulatedResults.length,
        successRate: Math.round((successfulResults.length / freeNodeResults.length) * 100)
      },
      results: finalResults,
      providers: {
        vercel: finalResults.filter(r => r.provider === 'vercel').length,
        cloudflare: finalResults.filter(r => r.provider === 'cloudflare').length,
        railway: finalResults.filter(r => r.provider === 'railway').length,
        flyio: finalResults.filter(r => r.provider === 'flyio').length,
        simulation: simulatedResults.length
      }
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('免费云节点测试失败:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
