#!/bin/bash

# 检查阿里云函数状态脚本

echo "🔍 检查阿里云函数计算部署状态"
echo "=================================="

# 切换到阿里云函数目录
cd cloud-functions/aliyun

echo "📋 获取函数信息..."
fun info

echo ""
echo "📊 检查各个函数的详细信息..."

# 检查各个服务和函数
services=("ping-service-beijing" "ping-service-shanghai" "ping-service-shenzhen" "ping-service-hangzhou" "ping-service-qingdao")

for service in "${services[@]}"; do
    echo ""
    echo "🔍 检查服务: $service"
    echo "----------------------------------------"
    
    # 获取服务信息
    fun info --service-name "$service" 2>/dev/null || echo "⚠️ 无法获取 $service 信息"
done

echo ""
echo "📈 检查函数调用日志..."
echo "使用以下命令查看具体函数的日志："
echo "fun logs -s ping-service-beijing -f ping-function-beijing"
echo "fun logs -s ping-service-shanghai -f ping-function-shanghai"

echo ""
echo "✅ 检查完成！"
