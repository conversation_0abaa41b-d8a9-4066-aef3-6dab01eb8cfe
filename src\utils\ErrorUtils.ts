// 错误处理工具类
export interface AppError {
  code: string;
  message: string;
  userMessage: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  context?: any;
}

export class ErrorUtils {
  // 错误代码映射
  private static readonly ERROR_CODES = {
    // 网络错误
    NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
    NETWORK_UNREACHABLE: 'NETWORK_UNREACHABLE',
    CORS_ERROR: 'CORS_ERROR',
    DNS_ERROR: 'DNS_ERROR',
    
    // 验证错误
    INVALID_URL: 'INVALID_URL',
    INVALID_PARAMS: 'INVALID_PARAMS',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    
    // API错误
    API_ERROR: 'API_ERROR',
    API_TIMEOUT: 'API_TIMEOUT',
    API_UNAUTHORIZED: 'API_UNAUTHORIZED',
    API_FORBIDDEN: 'API_FORBIDDEN',
    API_NOT_FOUND: 'API_NOT_FOUND',
    API_SERVER_ERROR: 'API_SERVER_ERROR',
    
    // 应用错误
    COMPONENT_ERROR: 'COMPONENT_ERROR',
    STORAGE_ERROR: 'STORAGE_ERROR',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR'
  };

  // 用户友好的错误消息
  private static readonly USER_MESSAGES = {
    [this.ERROR_CODES.NETWORK_TIMEOUT]: '网络连接超时，请检查网络连接后重试',
    [this.ERROR_CODES.NETWORK_UNREACHABLE]: '无法连接到目标服务器，请检查网址是否正确',
    [this.ERROR_CODES.CORS_ERROR]: '跨域请求被阻止，该网站可能不支持跨域访问',
    [this.ERROR_CODES.DNS_ERROR]: 'DNS解析失败，请检查域名是否正确',
    
    [this.ERROR_CODES.INVALID_URL]: '请输入有效的网址格式',
    [this.ERROR_CODES.INVALID_PARAMS]: '参数格式不正确，请检查输入',
    [this.ERROR_CODES.RATE_LIMIT_EXCEEDED]: '请求过于频繁，请稍后再试',
    
    [this.ERROR_CODES.API_ERROR]: '服务暂时不可用，请稍后重试',
    [this.ERROR_CODES.API_TIMEOUT]: '服务响应超时，请稍后重试',
    [this.ERROR_CODES.API_UNAUTHORIZED]: '访问未授权，请检查权限设置',
    [this.ERROR_CODES.API_FORBIDDEN]: '访问被禁止，请联系管理员',
    [this.ERROR_CODES.API_NOT_FOUND]: '请求的资源不存在',
    [this.ERROR_CODES.API_SERVER_ERROR]: '服务器内部错误，请稍后重试',
    
    [this.ERROR_CODES.COMPONENT_ERROR]: '页面组件出现错误，请刷新页面重试',
    [this.ERROR_CODES.STORAGE_ERROR]: '数据存储失败，请检查浏览器设置',
    [this.ERROR_CODES.UNKNOWN_ERROR]: '发生未知错误，请稍后重试'
  };

  // 创建应用错误
  static createError(
    code: string,
    originalError?: Error | string,
    context?: any
  ): AppError {
    const message = typeof originalError === 'string' 
      ? originalError 
      : originalError?.message || 'Unknown error';
    
    const userMessage = this.USER_MESSAGES[code] || this.USER_MESSAGES[this.ERROR_CODES.UNKNOWN_ERROR];
    
    const severity = this.getSeverity(code);
    
    return {
      code,
      message,
      userMessage,
      severity,
      timestamp: Date.now(),
      context
    };
  }

  // 从HTTP错误创建应用错误
  static fromHttpError(status: number, message?: string, context?: any): AppError {
    let code: string;
    
    switch (status) {
      case 400:
        code = this.ERROR_CODES.INVALID_PARAMS;
        break;
      case 401:
        code = this.ERROR_CODES.API_UNAUTHORIZED;
        break;
      case 403:
        code = this.ERROR_CODES.API_FORBIDDEN;
        break;
      case 404:
        code = this.ERROR_CODES.API_NOT_FOUND;
        break;
      case 408:
        code = this.ERROR_CODES.API_TIMEOUT;
        break;
      case 429:
        code = this.ERROR_CODES.RATE_LIMIT_EXCEEDED;
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        code = this.ERROR_CODES.API_SERVER_ERROR;
        break;
      default:
        code = this.ERROR_CODES.API_ERROR;
    }
    
    return this.createError(code, message, { ...context, httpStatus: status });
  }

  // 从网络错误创建应用错误
  static fromNetworkError(error: Error, context?: any): AppError {
    const message = error.message.toLowerCase();
    
    let code: string;
    
    if (message.includes('timeout')) {
      code = this.ERROR_CODES.NETWORK_TIMEOUT;
    } else if (message.includes('cors')) {
      code = this.ERROR_CODES.CORS_ERROR;
    } else if (message.includes('dns') || message.includes('resolve')) {
      code = this.ERROR_CODES.DNS_ERROR;
    } else if (message.includes('unreachable') || message.includes('refused')) {
      code = this.ERROR_CODES.NETWORK_UNREACHABLE;
    } else {
      code = this.ERROR_CODES.NETWORK_UNREACHABLE;
    }
    
    return this.createError(code, error, context);
  }

  // 获取错误严重程度
  private static getSeverity(code: string): 'low' | 'medium' | 'high' | 'critical' {
    const criticalErrors = [
      this.ERROR_CODES.API_SERVER_ERROR,
      this.ERROR_CODES.COMPONENT_ERROR
    ];
    
    const highErrors = [
      this.ERROR_CODES.API_UNAUTHORIZED,
      this.ERROR_CODES.API_FORBIDDEN,
      this.ERROR_CODES.STORAGE_ERROR
    ];
    
    const mediumErrors = [
      this.ERROR_CODES.NETWORK_TIMEOUT,
      this.ERROR_CODES.API_TIMEOUT,
      this.ERROR_CODES.RATE_LIMIT_EXCEEDED
    ];
    
    if (criticalErrors.includes(code)) return 'critical';
    if (highErrors.includes(code)) return 'high';
    if (mediumErrors.includes(code)) return 'medium';
    return 'low';
  }

  // 判断错误是否可重试
  static isRetryable(error: AppError): boolean {
    const retryableCodes = [
      this.ERROR_CODES.NETWORK_TIMEOUT,
      this.ERROR_CODES.API_TIMEOUT,
      this.ERROR_CODES.API_SERVER_ERROR,
      this.ERROR_CODES.NETWORK_UNREACHABLE
    ];
    
    return retryableCodes.includes(error.code);
  }

  // 获取重试延迟（指数退避）
  static getRetryDelay(attempt: number, baseDelay = 1000): number {
    return Math.min(baseDelay * Math.pow(2, attempt), 30000); // 最大30秒
  }

  // 格式化错误用于显示
  static formatForDisplay(error: AppError): {
    title: string;
    message: string;
    canRetry: boolean;
    severity: string;
  } {
    return {
      title: this.getErrorTitle(error.severity),
      message: error.userMessage,
      canRetry: this.isRetryable(error),
      severity: error.severity
    };
  }

  // 获取错误标题
  private static getErrorTitle(severity: string): string {
    switch (severity) {
      case 'critical':
        return '严重错误';
      case 'high':
        return '错误';
      case 'medium':
        return '警告';
      default:
        return '提示';
    }
  }

  // 错误日志记录
  static log(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logData = {
      ...error,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'SSR',
      url: typeof window !== 'undefined' ? window.location.href : 'SSR'
    };
    
    switch (logLevel) {
      case 'error':
        console.error('App Error:', logData);
        break;
      case 'warn':
        console.warn('App Warning:', logData);
        break;
      default:
        console.log('App Info:', logData);
    }
    
    // 发送到监控服务
    this.sendToMonitoring(error);
  }

  // 获取日志级别
  private static getLogLevel(severity: string): 'error' | 'warn' | 'info' {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warn';
      default:
        return 'info';
    }
  }

  // 发送到监控服务
  private static sendToMonitoring(error: AppError): void {
    if (typeof window !== 'undefined' && error.severity !== 'low') {
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(error)
      }).catch(() => {
        // 静默处理监控服务错误
      });
    }
  }
}

export default ErrorUtils;
