// 多云部署网络测试节点配置
export interface TestNode {
  id: string;
  name: string;
  provider: 'aliyun' | 'tencent' | 'huawei' | 'baidu';
  region: string;
  city: string;
  province: string;
  endpoint: string;
  latitude: number;
  longitude: number;
  networkType: 'BGP' | '电信' | '联通' | '移动';
  status: 'active' | 'inactive' | 'maintenance';
}

// 中国境内测试节点配置
export const CHINA_TEST_NODES: TestNode[] = [
  // 阿里云节点
  {
    id: 'aliyun-beijing',
    name: '北京',
    provider: 'aliyun',
    region: 'cn-beijing',
    city: '北京',
    province: '北京',
    endpoint: 'https://ping-beijing.your-domain.com',
    latitude: 39.9042,
    longitude: 116.4074,
    networkType: 'BGP',
    status: 'active'
  },
  {
    id: 'aliyun-shanghai',
    name: '上海',
    provider: 'aliyun',
    region: 'cn-shanghai',
    city: '上海',
    province: '上海',
    endpoint: 'https://ping-shanghai.your-domain.com',
    latitude: 31.2304,
    longitude: 121.4737,
    networkType: 'BGP',
    status: 'active'
  },
  {
    id: 'aliyun-shenzhen',
    name: '深圳',
    provider: 'aliyun',
    region: 'cn-shenzhen',
    city: '深圳',
    province: '广东',
    endpoint: 'https://ping-shenzhen.your-domain.com',
    latitude: 22.5431,
    longitude: 114.0579,
    networkType: 'BGP',
    status: 'active'
  },
  
  // 腾讯云节点
  {
    id: 'tencent-guangzhou',
    name: '广州',
    provider: 'tencent',
    region: 'ap-guangzhou',
    city: '广州',
    province: '广东',
    endpoint: 'https://ping-guangzhou.your-domain.com',
    latitude: 23.1291,
    longitude: 113.2644,
    networkType: 'BGP',
    status: 'active'
  },
  {
    id: 'tencent-chengdu',
    name: '成都',
    provider: 'tencent',
    region: 'ap-chengdu',
    city: '成都',
    province: '四川',
    endpoint: 'https://ping-chengdu.your-domain.com',
    latitude: 30.5728,
    longitude: 104.0668,
    networkType: 'BGP',
    status: 'active'
  },
  
  // 华为云节点
  {
    id: 'huawei-xian',
    name: '西安',
    provider: 'huawei',
    region: 'cn-northwest-1',
    city: '西安',
    province: '陕西',
    endpoint: 'https://ping-xian.your-domain.com',
    latitude: 34.3416,
    longitude: 108.9398,
    networkType: 'BGP',
    status: 'active'
  }
];

// 多云网络测试服务
export class MultiCloudNetworkTester {
  private nodes: TestNode[];
  
  constructor(nodes: TestNode[] = CHINA_TEST_NODES) {
    this.nodes = nodes.filter(node => node.status === 'active');
  }

  // 选择最优测试节点
  selectOptimalNodes(maxNodes: number = 10): TestNode[] {
    // 按省份分组，确保覆盖更多地区
    const provinceGroups = this.groupByProvince();
    const selectedNodes: TestNode[] = [];

    // 优先选择一线城市节点
    const priorityCities = ['北京', '上海', '广州', '深圳'];
    
    for (const city of priorityCities) {
      const node = this.nodes.find(n => n.city === city);
      if (node && selectedNodes.length < maxNodes) {
        selectedNodes.push(node);
      }
    }

    // 填充其他省份节点
    for (const [province, nodes] of Object.entries(provinceGroups)) {
      if (selectedNodes.length >= maxNodes) break;
      
      const existingProvince = selectedNodes.find(n => n.province === province);
      if (!existingProvince && nodes.length > 0) {
        // 选择该省份最好的节点（BGP优先）
        const bestNode = nodes.sort((a, b) => {
          if (a.networkType === 'BGP' && b.networkType !== 'BGP') return -1;
          if (a.networkType !== 'BGP' && b.networkType === 'BGP') return 1;
          return 0;
        })[0];
        
        selectedNodes.push(bestNode);
      }
    }

    return selectedNodes;
  }

  private groupByProvince(): Record<string, TestNode[]> {
    return this.nodes.reduce((groups, node) => {
      if (!groups[node.province]) {
        groups[node.province] = [];
      }
      groups[node.province].push(node);
      return groups;
    }, {} as Record<string, TestNode[]>);
  }

  // 并发测试所有节点
  async testAllNodes(target: string): Promise<Array<{
    node: TestNode;
    latency: number;
    status: 'success' | 'failed';
    error?: string;
  }>> {
    const selectedNodes = this.selectOptimalNodes();
    
    const testPromises = selectedNodes.map(async (node) => {
      try {
        const latency = await this.testSingleNode(node, target);
        return {
          node,
          latency,
          status: 'success' as const
        };
      } catch (error) {
        return {
          node,
          latency: 0,
          status: 'failed' as const,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    return await Promise.all(testPromises);
  }

  // 测试单个节点
  private async testSingleNode(node: TestNode, target: string): Promise<number> {
    const testUrl = `${node.endpoint}/api/ping`;
    
    const start = performance.now();
    
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        target,
        timeout: 5000
      }),
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const totalTime = performance.now() - start;

    // 返回服务器端测试的延迟，而不是到服务器的延迟
    return data.latency || totalTime;
  }

  // 获取节点健康状态
  async checkNodesHealth(): Promise<Array<{
    node: TestNode;
    healthy: boolean;
    responseTime: number;
    lastCheck: Date;
  }>> {
    const healthPromises = this.nodes.map(async (node) => {
      const start = performance.now();
      
      try {
        const response = await fetch(`${node.endpoint}/health`, {
          signal: AbortSignal.timeout(5000)
        });
        
        const responseTime = performance.now() - start;
        
        return {
          node,
          healthy: response.ok,
          responseTime: Math.round(responseTime),
          lastCheck: new Date()
        };
      } catch (error) {
        return {
          node,
          healthy: false,
          responseTime: 0,
          lastCheck: new Date()
        };
      }
    });

    return await Promise.all(healthPromises);
  }
}

// 部署配置生成器
export class DeploymentConfigGenerator {
  // 生成阿里云函数计算配置
  generateAliyunFCConfig(node: TestNode) {
    return {
      service: `ping-service-${node.region}`,
      function: `ping-function-${node.city}`,
      runtime: 'nodejs18',
      handler: 'index.handler',
      timeout: 30,
      memorySize: 512,
      environmentVariables: {
        NODE_ENV: 'production',
        REGION: node.region,
        CITY: node.city,
        PROVINCE: node.province
      },
      triggers: [
        {
          name: 'httpTrigger',
          type: 'http',
          config: {
            authType: 'anonymous',
            methods: ['POST', 'GET']
          }
        }
      ]
    };
  }

  // 生成腾讯云SCF配置
  generateTencentSCFConfig(node: TestNode) {
    return {
      functionName: `ping-function-${node.city}`,
      runtime: 'Nodejs18.15',
      handler: 'index.main_handler',
      timeout: 30,
      memorySize: 512,
      environment: {
        variables: {
          REGION: node.region,
          CITY: node.city,
          PROVINCE: node.province
        }
      },
      triggers: [
        {
          name: 'apigw',
          type: 'apigw',
          config: {
            protocol: 'https',
            environment: 'release',
            endpoints: [
              {
                path: '/ping',
                method: 'POST'
              }
            ]
          }
        }
      ]
    };
  }

  // 生成Docker部署配置
  generateDockerConfig(node: TestNode) {
    return {
      image: 'ping-tester:latest',
      ports: ['3000:3000'],
      environment: [
        `REGION=${node.region}`,
        `CITY=${node.city}`,
        `PROVINCE=${node.province}`,
        `NETWORK_TYPE=${node.networkType}`
      ],
      labels: {
        'ping.node.id': node.id,
        'ping.node.provider': node.provider,
        'ping.node.region': node.region
      },
      healthcheck: {
        test: ['CMD', 'curl', '-f', 'http://localhost:3000/health'],
        interval: '30s',
        timeout: '10s',
        retries: 3
      }
    };
  }
}
