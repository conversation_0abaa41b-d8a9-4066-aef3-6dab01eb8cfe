import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  // 在构建时忽略ESLint错误
  eslint: {
    ignoreDuringBuilds: true,
  },
  // 在构建时忽略TypeScript错误
  typescript: {
    ignoreBuildErrors: true,
  },
  // 禁用构建缓存以避免大文件问题
  experimental: {
    webpackBuildWorker: false,
  },
  // 配置 webpack 以减少输出大小
  webpack: (config, { isServer }) => {
    // 禁用缓存
    config.cache = false;
    return config;
  },
};

export default nextConfig;
