'use client';

import React, { useState } from 'react';

interface TestResult {
  target: string;
  latency: number;
  originalLatency: number;
  isDomestic: boolean;
  benchmark: {
    domestic: number;
    foreign: number;
    threshold: number;
  };
  service: string;
  timestamp: string;
}

export default function SmartDetectionTestPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [customUrl, setCustomUrl] = useState('');

  // 预设测试网站
  const testSites = [
    { name: '百度 (国内)', url: 'https://www.baidu.com' },
    { name: 'Google (国外)', url: 'https://www.google.com' },
    { name: 'GitHub (国外)', url: 'https://github.com' },
    { name: '淘宝 (国内)', url: 'https://www.taobao.com' },
    { name: 'YouTube (国外)', url: 'https://www.youtube.com' },
    { name: '微博 (国内)', url: 'https://weibo.com' },
    { name: 'wobshare (基准国内)', url: 'https://wobshare.us.kg' },
    { name: 'Stack Overflow (国外)', url: 'https://stackoverflow.com' }
  ];

  const runSmartDetectionTest = async (targetUrl: string) => {
    setIsRunning(true);

    try {
      // 测试 Cloudflare Workers
      const cfResponse = await fetch('/api/ping-cloudflare-worker', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: targetUrl }),
      });

      if (cfResponse.ok) {
        const cfData = await cfResponse.json();
        
        if (cfData.success && cfData.testResult) {
          const result: TestResult = {
            target: targetUrl,
            latency: cfData.testResult.latency || cfData.latency,
            originalLatency: cfData.testResult.originalLatency || cfData.latency,
            isDomestic: cfData.testResult.isDomestic || false,
            benchmark: cfData.testResult.benchmark || { domestic: 100, foreign: 300, threshold: 200 },
            service: 'Cloudflare Workers',
            timestamp: new Date().toISOString()
          };

          setResults(prev => [result, ...prev]);
        }
      }

      // 测试 Vercel Edge Functions
      const vercelResponse = await fetch('/api/ping-vercel-edge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: targetUrl }),
      });

      if (vercelResponse.ok) {
        const vercelData = await vercelResponse.json();
        
        if (vercelData.success && vercelData.testResult) {
          const result: TestResult = {
            target: targetUrl,
            latency: vercelData.testResult.latency || vercelData.latency,
            originalLatency: vercelData.testResult.originalLatency || vercelData.latency,
            isDomestic: vercelData.testResult.isDomestic || false,
            benchmark: vercelData.testResult.benchmark || { domestic: 100, foreign: 300, threshold: 200 },
            service: 'Vercel Edge Functions',
            timestamp: new Date().toISOString()
          };

          setResults(prev => [result, ...prev]);
        }
      }

    } catch (error) {
      console.error('测试失败:', error);
    }

    setIsRunning(false);
  };

  const runBatchTest = async () => {
    setResults([]);
    for (const site of testSites) {
      await runSmartDetectionTest(site.url);
      // 短暂延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const runCustomTest = async () => {
    if (!customUrl.trim()) return;
    await runSmartDetectionTest(customUrl.trim());
  };

  const getStatusColor = (isDomestic: boolean) => {
    return isDomestic ? 'text-blue-400' : 'text-orange-400';
  };

  const getStatusIcon = (isDomestic: boolean) => {
    return isDomestic ? '🇨🇳' : '🌍';
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          🧠 智能网站类型检测测试
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* 批量测试 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">📊 批量测试</h2>
            <p className="text-gray-400 mb-4">
              测试预设的国内外网站，验证智能判断算法的准确性
            </p>
            
            <button
              onClick={runBatchTest}
              disabled={isRunning}
              className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                isRunning
                  ? 'bg-gray-600 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isRunning ? '🔄 测试中...' : '🚀 开始批量测试'}
            </button>

            <div className="mt-4 space-y-2">
              {testSites.map((site, index) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span className="text-gray-300">{site.name}</span>
                  <span className="text-gray-500 font-mono">{site.url}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 自定义测试 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">🎯 自定义测试</h2>
            <p className="text-gray-400 mb-4">
              输入任意网站URL，测试智能判断功能
            </p>
            
            <div className="space-y-4">
              <input
                type="url"
                value={customUrl}
                onChange={(e) => setCustomUrl(e.target.value)}
                placeholder="https://example.com"
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              
              <button
                onClick={runCustomTest}
                disabled={isRunning || !customUrl.trim()}
                className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                  isRunning || !customUrl.trim()
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {isRunning ? '🔄 测试中...' : '🧪 测试此网站'}
              </button>
            </div>
          </div>
        </div>

        {/* 测试结果 */}
        {results.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">📋 测试结果</h2>
            
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className="p-4 bg-gray-700 rounded-lg border-l-4 border-blue-500"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getStatusIcon(result.isDomestic)}</span>
                      <span className="font-semibold">{result.target}</span>
                    </div>
                    <span className="text-sm text-gray-400">{result.service}</span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">判定结果:</span>
                      <span className={`ml-2 font-mono ${getStatusColor(result.isDomestic)}`}>
                        {result.isDomestic ? '🇨🇳 国内网站' : '🌍 国外网站'}
                      </span>
                    </div>
                    
                    <div>
                      <span className="text-gray-400">校准延迟:</span>
                      <span className="ml-2 font-mono text-green-400">
                        {result.latency}ms
                      </span>
                    </div>
                    
                    <div>
                      <span className="text-gray-400">原始延迟:</span>
                      <span className="ml-2 font-mono text-yellow-400">
                        {result.originalLatency}ms
                      </span>
                    </div>
                    
                    <div>
                      <span className="text-gray-400">判定阈值:</span>
                      <span className="ml-2 font-mono text-purple-400">
                        {result.benchmark.threshold}ms
                      </span>
                    </div>
                  </div>

                  <div className="mt-2 text-xs text-gray-500">
                    基准: 国内 {result.benchmark.domestic}ms | 国外 {result.benchmark.foreign}ms | 
                    时间: {new Date(result.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
              <h3 className="font-semibold mb-2">🔍 算法说明</h3>
              <div className="text-sm text-blue-300 space-y-1">
                <p>• 动态测试 wobshare.us.kg (国内基准) 和 google.com (国外基准)</p>
                <p>• 计算两者延迟的中间值作为判定阈值</p>
                <p>• 小于阈值 → 国内网站，应用国内延迟倍数</p>
                <p>• 大于阈值 → 国外网站，应用国外延迟倍数</p>
                <p>• 根据判定结果智能调整最终延迟值</p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <a
            href="/"
            className="inline-block px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
          >
            🏠 返回主页面
          </a>
        </div>
      </div>
    </div>
  );
}
