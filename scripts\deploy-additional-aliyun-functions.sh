#!/bin/bash

# 部署额外的阿里云函数节点脚本
# 用于部署广州、成都、重庆、南京节点

echo "🚀 开始部署额外的阿里云函数节点..."
echo "=================================="

# 检查fun工具是否安装
if ! command -v fun &> /dev/null; then
    echo "❌ Fun工具未安装，请先安装Funcraft"
    echo "npm install @alicloud/fun -g"
    exit 1
fi

# 检查阿里云配置
if [ ! -f ~/.fcli/config.yaml ]; then
    echo "❌ 阿里云配置未找到，请先配置阿里云凭证"
    echo "fun config"
    exit 1
fi

# 切换到阿里云函数目录
cd cloud-functions/aliyun

echo "📋 当前部署的新节点:"
echo "- 广州 (cn-guangzhou)"
echo "- 成都 (cn-chengdu)"
echo "- 重庆 (cn-chongqing)"
echo "- 南京 (cn-nanjing)"
echo ""

# 部署新的函数服务
echo "🔨 开始部署函数..."

# 部署广州节点
echo "📍 部署广州节点..."
fun deploy --service ping-service-guangzhou --function ping-function-guangzhou

if [ $? -eq 0 ]; then
    echo "✅ 广州节点部署成功"
else
    echo "❌ 广州节点部署失败"
fi

# 部署成都节点
echo "📍 部署成都节点..."
fun deploy --service ping-service-chengdu --function ping-function-chengdu

if [ $? -eq 0 ]; then
    echo "✅ 成都节点部署成功"
else
    echo "❌ 成都节点部署失败"
fi

# 部署重庆节点
echo "📍 部署重庆节点..."
fun deploy --service ping-service-chongqing --function ping-function-chongqing

if [ $? -eq 0 ]; then
    echo "✅ 重庆节点部署成功"
else
    echo "❌ 重庆节点部署失败"
fi

# 部署南京节点
echo "📍 部署南京节点..."
fun deploy --service ping-service-nanjing --function ping-function-nanjing

if [ $? -eq 0 ]; then
    echo "✅ 南京节点部署成功"
else
    echo "❌ 南京节点部署失败"
fi

echo ""
echo "🎉 部署完成！"
echo "=================================="

# 获取函数信息
echo "📊 获取函数端点信息..."
echo ""

# 显示新部署的函数端点
echo "🔗 新增的函数端点:"
echo "ALIYUN_GUANGZHOU_ENDPOINT=https://1322152058828090.cn-guangzhou.fc.aliyuncs.com/2016-08-15/proxy/ping-service-guangzhou/ping-function-guangzhou/"
echo "ALIYUN_CHENGDU_ENDPOINT=https://1322152058828090.cn-chengdu.fc.aliyuncs.com/2016-08-15/proxy/ping-service-chengdu/ping-function-chengdu/"
echo "ALIYUN_CHONGQING_ENDPOINT=https://1322152058828090.cn-chongqing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-chongqing/ping-function-chongqing/"
echo "ALIYUN_NANJING_ENDPOINT=https://1322152058828090.cn-nanjing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-nanjing/ping-function-nanjing/"

echo ""
echo "📋 请将以上环境变量添加到Vercel项目设置中"
echo "✅ 部署脚本执行完成！"
