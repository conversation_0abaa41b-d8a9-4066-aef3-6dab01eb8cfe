# Cloudflare Workers 配置文件
# 用于部署全球网络延迟测试服务

name = "ping-test-worker"
main = "ping-cloudflare.js"
compatibility_date = "2024-01-01"

# 环境变量
[vars]
ENVIRONMENT = "production"
SERVICE_NAME = "Ping Test Service"
VERSION = "1.0.0"

# 路由配置 (可选，如果有自定义域名)
# routes = [
#   { pattern = "ping.your-domain.com/*", zone_name = "your-domain.com" }
# ]

# KV 存储 (可选，用于缓存和统计)
# [[kv_namespaces]]
# binding = "PING_CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# 触发器配置
[triggers]
crons = []

# 构建配置
[build]
command = ""
cwd = ""
watch_dir = ""

# 部署配置
[env.production]
name = "ping-test-worker"
vars = { ENVIRONMENT = "production" }

[env.staging]
name = "ping-test-worker-staging"
vars = { ENVIRONMENT = "staging" }

# 限制配置
[limits]
cpu_ms = 50
