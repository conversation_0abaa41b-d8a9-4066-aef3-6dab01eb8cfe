#!/usr/bin/env node

// 检查阿里云函数状态和生成监控链接
console.log('🔍 阿里云函数状态检查');
console.log('=' .repeat(50));

const functions = [
  { name: '北京', service: 'ping-service-beijing', function: 'ping-function-beijing', region: 'cn-beijing' },
  { name: '上海', service: 'ping-service-shanghai', function: 'ping-function-shanghai', region: 'cn-shanghai' },
  { name: '深圳', service: 'ping-service-shenzhen', function: 'ping-function-shenzhen', region: 'cn-shenzhen' },
  { name: '杭州', service: 'ping-service-hangzhou', function: 'ping-function-hangzhou', region: 'cn-hangzhou' },
  { name: '青岛', service: 'ping-service-qingdao', function: 'ping-function-qingdao', region: 'cn-qingdao' }
];

console.log('📊 函数部署信息:');
functions.forEach((func, index) => {
  console.log(`${index + 1}. ${func.name}`);
  console.log(`   服务: ${func.service}`);
  console.log(`   函数: ${func.function}`);
  console.log(`   地区: ${func.region}`);
  console.log(`   URL: https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/${func.service}/${func.function}/`);
  console.log('');
});

console.log('🌐 阿里云控制台链接:');
console.log('=' .repeat(50));
console.log('📋 函数计算控制台: https://fc.console.aliyun.com/');
console.log('📊 监控中心: https://cloudmonitor.console.aliyun.com/');
console.log('💰 费用中心: https://expense.console.aliyun.com/');

console.log('\n📈 查看函数监控数据:');
functions.forEach((func, index) => {
  const consoleUrl = `https://fc.console.aliyun.com/fc/service/${func.region}/${func.service}/function/${func.function}/monitor`;
  console.log(`${index + 1}. ${func.name}: ${consoleUrl}`);
});

console.log('\n🔧 本地测试命令:');
console.log('=' .repeat(50));
console.log('使用JSON文件测试:');
console.log('curl -X POST -H "Content-Type: application/json" -d @test-data.json "FUNCTION_URL"');
console.log('');
console.log('使用Node.js测试:');
console.log('node scripts/test-aliyun-local.js');
console.log('node scripts/test-aliyun-local.js https://taobao.com');

console.log('\n✅ 状态检查完成！');
console.log('💡 访问控制台链接查看详细的函数运行状态和日志');
