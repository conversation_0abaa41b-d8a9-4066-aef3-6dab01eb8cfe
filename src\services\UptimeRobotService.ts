// UptimeRobot API 集成服务
export class UptimeRobotService {
  private apiKey: string;
  private baseUrl = 'https://api.uptimerobot.com/v2';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  // 创建监控器
  async createMonitor(url: string, friendlyName: string) {
    const response = await fetch(`${this.baseUrl}/newMonitor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: this.apiKey,
        format: 'json',
        type: '1', // HTTP(s)
        url: url,
        friendly_name: friendlyName,
        interval: '300' // 5分钟检查一次
      })
    });

    return await response.json();
  }

  // 获取监控器状态
  async getMonitors() {
    const response = await fetch(`${this.baseUrl}/getMonitors`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: this.apiKey,
        format: 'json',
        response_times: '1',
        response_times_limit: '24' // 最近24小时
      })
    });

    return await response.json();
  }

  // 获取响应时间数据
  async getResponseTimes(monitorId: string) {
    const response = await fetch(`${this.baseUrl}/getMonitors`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: this.apiKey,
        format: 'json',
        monitors: monitorId,
        response_times: '1',
        response_times_limit: '50'
      })
    });

    const data = await response.json();
    return data.monitors?.[0]?.response_times || [];
  }
}

// 多服务商网络监控聚合器
export class NetworkMonitoringAggregator {
  private services: {
    uptimeRobot?: UptimeRobotService;
    // 可以添加更多服务
  } = {};

  constructor(config: {
    uptimeRobotApiKey?: string;
  }) {
    if (config.uptimeRobotApiKey) {
      this.services.uptimeRobot = new UptimeRobotService(config.uptimeRobotApiKey);
    }
  }

  // 聚合多个服务的数据
  async getAggregatedNetworkData(url: string) {
    const results: any[] = [];

    // UptimeRobot数据
    if (this.services.uptimeRobot) {
      try {
        const monitors = await this.services.uptimeRobot.getMonitors();
        const relevantMonitor = monitors.monitors?.find((m: any) => m.url === url);
        
        if (relevantMonitor) {
          const responseTimes = await this.services.uptimeRobot.getResponseTimes(relevantMonitor.id);
          results.push({
            provider: 'UptimeRobot',
            data: responseTimes,
            uptime: relevantMonitor.all_time_uptime_ratio
          });
        }
      } catch (error) {
        console.error('UptimeRobot API error:', error);
      }
    }

    return results;
  }
}
