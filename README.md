# 🌐 Ping 网络延迟测试工具

基于阿里云函数计算的真实网络延迟测试工具，提供准确的中国大陆网络连通性测试。

## ✨ 核心特性

- 🎯 **真实测试**: 使用阿里云函数计算进行真实网络测试
- 🌍 **多节点覆盖**: 北京、上海、深圳、杭州、青岛 5个节点
- ⚡ **快速响应**: 优化的并发测试，6秒超时
- 📊 **准确结果**: 反映真实的中国大陆网络状况
- 🎨 **可视化**: 中国地图展示，延迟热力图
- 📱 **响应式**: 支持桌面和移动设备

## 🚀 快速开始

### 环境 要求

- Node.js 18+
- npm 或 yarn
- 阿里云账号（用于函数计算部署）

### 本地运行

```bash
# 克隆项目
git clone <repository-url>
cd ping

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问应用
open http://localhost:3000
```

### 生产部署

```bash
# 构建项目
npm run build

# 启动生产服务器
npm start
```

## 🏗️ 项目结构

```
ping/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API 路由
│   │   │   ├── ping-cloudping/ # 主要测试API
│   │   │   └── health/        # 健康检查
│   │   ├── page.tsx           # 主页面
│   │   └── globals.css        # 全局样式
│   ├── components/            # React 组件
│   │   ├── PingTester.tsx     # 主测试组件
│   │   ├── ChinaMap.tsx       # 中国地图组件
│   │   └── ui/                # UI 组件库
│   ├── services/              # 业务逻辑
│   └── utils/                 # 工具函数
├── cloud-functions/
│   └── aliyun/                # 阿里云函数代码
│       ├── index.js           # 函数入口
│       └── template.yml       # 部署配置
├── scripts/                   # 部署和测试脚本
└── public/                    # 静态资源
```

## 🔧 配置说明

### 环境变量

创建 `.env.local` 文件：

```env
# 阿里云配置（可选，用于部署）
ALIYUN_ACCESS_KEY_ID=your_access_key
ALIYUN_ACCESS_KEY_SECRET=your_secret_key
ALIYUN_REGION=cn-beijing

# 应用配置
NEXT_PUBLIC_APP_NAME=Ping测试工具
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 阿里云函数端点

在 `src/app/api/ping-cloudping/route.ts` 中配置：

```javascript
const aliyunNodes = [
  { name: '北京', endpoint: 'https://your-function-url/beijing/', provider: 'aliyun', region: 'cn-beijing' },
  { name: '上海', endpoint: 'https://your-function-url/shanghai/', provider: 'aliyun', region: 'cn-shanghai' },
  // ... 其他节点
];
```

## 📡 API 接口

### POST /api/ping-cloudping

主要的网络测试接口

**请求体:**
```json
{
  "target": "https://example.com"
}
```

**响应:**
```json
{
  "success": true,
  "results": [
    {
      "node": "北京",
      "ping": 45,
      "status": "success",
      "location": {
        "city": "北京",
        "province": "北京",
        "country": "China"
      },
      "testMethod": "AliyunFC-RealPing",
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  ],
  "metadata": {
    "target": "https://example.com",
    "totalNodes": 5,
    "successfulNodes": 5,
    "testType": "real-multicloud"
  }
}
```

### GET /api/health

健康检查接口

**响应:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": {
    "nodeVersion": "v18.17.0",
    "platform": "linux"
  }
}
```

## 🌐 阿里云函数部署

### 1. 安装阿里云CLI工具

```bash
# 安装 Funcraft
npm install -g @alicloud/fun

# 配置阿里云账号
fun config
```

### 2. 部署函数

```bash
# 进入函数目录
cd cloud-functions/aliyun

# 部署所有函数
fun deploy

# 查看部署状态
fun info
```

### 3. 获取函数URL

部署完成后，更新 `src/app/api/ping-cloudping/route.ts` 中的端点URL。

## 🧪 测试

### 单元测试

```bash
npm test
```

### 集成测试

```bash
# 测试阿里云函数
node scripts/test-aliyun-local.js

# 测试API准确性
node scripts/test-google-accuracy.js
```

### 手动测试

```bash
# 测试本地API
curl -X POST -H "Content-Type: application/json" \
  -d '{"target":"https://baidu.com"}' \
  http://localhost:3000/api/ping-cloudping
```

## 📊 测试逻辑说明

### 测试流程

1. **接收请求**: 用户输入目标URL
2. **并发调用**: 同时调用5个阿里云函数节点
3. **真实测试**: 每个节点执行TCP连接和HTTP请求测试
4. **结果聚合**: 收集所有节点的测试结果
5. **数据展示**: 在地图上可视化显示延迟数据

### 测试方法

每个阿里云函数执行以下测试：

1. **TCP连接测试**: 直接TCP连接到目标主机
2. **HTTP请求测试**: 发送HTTP HEAD请求
3. **延迟计算**: 取两种方法的平均值
4. **错误处理**: 超时或失败时返回5000ms

### 准确性保证

- ✅ **真实网络环境**: 阿里云函数运行在真实的中国大陆网络环境
- ✅ **多点验证**: 5个不同地理位置的节点同时测试
- ✅ **超时控制**: 6秒超时，避免长时间等待
- ✅ **错误处理**: 完善的错误处理和降级机制

## ⚠️ 注意事项

### 网络限制

- 某些网站可能在中国大陆无法访问（如Google、Facebook等）
- 测试结果会正确显示为高延迟或无法连接
- 国内网站（如百度、腾讯等）会显示正常的低延迟

### 使用限制

- 阿里云函数有调用次数限制（免费额度内通常足够）
- 建议合理使用，避免频繁测试同一目标
- 生产环境建议配置监控和告警

### 数据准确性

- 测试结果反映的是从阿里云数据中心到目标服务器的网络延迟
- 实际用户体验可能因ISP、地理位置等因素有所不同
- 建议将测试结果作为参考，而非绝对标准

## 🔧 故障排除

### 常见问题

1. **函数调用失败**
   - 检查阿里云函数是否正常部署
   - 验证函数URL是否正确
   - 确认网络连接正常

2. **测试结果不准确**
   - 确认使用的是真实的阿里云函数端点
   - 检查函数代码是否为最新版本
   - 验证目标URL是否可访问

3. **页面加载缓慢**
   - 检查网络连接
   - 确认API响应时间
   - 查看浏览器控制台错误

### 调试方法

```bash
# 查看函数日志
fun logs -s ping-service-beijing -f ping-function-beijing

# 测试单个节点
curl -X POST -H "Content-Type: application/json" \
  -d '{"target":"https://baidu.com"}' \
  https://your-aliyun-function-url/

# 检查API响应
curl -X POST -H "Content-Type: application/json" \
  -d '{"target":"https://baidu.com"}' \
  http://localhost:3000/api/ping-cloudping
```

## 📈 性能优化

- 使用并发请求减少总测试时间
- 6秒超时平衡准确性和速度
- 客户端缓存减少重复请求
- 响应式设计优化移动端体验

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🆘 支持

如有问题或建议，请创建 Issue 或联系维护者。
