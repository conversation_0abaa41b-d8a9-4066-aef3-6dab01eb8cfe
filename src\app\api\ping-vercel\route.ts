import { NextRequest, NextResponse } from 'next/server';

// Vercel Edge Functions 节点配置
const VERCEL_EDGE_REGIONS = [
  { name: '香港', region: 'hkg1', city: '香港', country: 'HK' },
  { name: '新加坡', region: 'sin1', city: '新加坡', country: 'SG' },
  { name: '东京', region: 'nrt1', city: '东京', country: 'JP' },
  { name: '首尔', region: 'icn1', city: '首尔', country: 'KR' },
  { name: '旧金山', region: 'sfo1', city: '旧金山', country: 'US' },
  { name: '华盛顿', region: 'iad1', city: '华盛顿', country: 'US' },
  { name: '法兰克福', region: 'fra1', city: '法兰克福', country: 'DE' },
  { name: '伦敦', region: 'lhr1', city: '伦敦', country: 'GB' },
  { name: '悉尼', region: 'syd1', city: '悉尼', country: 'AU' },
  { name: '孟买', region: 'bom1', city: '孟买', country: 'IN' }
];

// 调用 Vercel Edge Function 进行测试
async function callVercelEdgeFunction(target: string, timeout: number = 8000): Promise<any[]> {
  const results: any[] = [];
  
  try {
    // 获取当前域名
    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}` 
      : process.env.NODE_ENV === 'development' 
        ? 'http://localhost:3000'
        : 'https://ping.wobshare.us.kg'; // 您的生产域名
    
    console.log(`🌐 调用 Vercel Edge Function: ${baseUrl}/api/ping-vercel-edge`);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const startTime = Date.now();
    const response = await fetch(`${baseUrl}/api/ping-vercel-edge`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vercel-Edge-Ping-Aggregator/1.0'
      },
      body: JSON.stringify({ 
        target,
        testCount: 2 // 减少测试次数提高速度
      }),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    const requestTime = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      // 根据响应的地理位置信息确定节点
      const detectedRegion = VERCEL_EDGE_REGIONS.find(r => 
        r.country === data.location?.country || 
        r.city === data.location?.city
      ) || VERCEL_EDGE_REGIONS[0]; // 默认使用香港节点
      
      results.push({
        success: true,
        name: detectedRegion.name,
        provider: 'vercel-edge',
        region: detectedRegion.region,
        city: detectedRegion.city,
        country: detectedRegion.country,
        latency: data.averageLatency || data.medianLatency || null,
        minLatency: data.minLatency || null,
        maxLatency: data.maxLatency || null,
        successRate: data.successRate || 100,
        requestTime,
        testMethod: 'Vercel-Edge-HTTP-HEAD',
        location: data.location || {},
        timestamp: new Date().toISOString(),
        rawData: data
      });
    } else {
      results.push({
        success: false,
        name: 'Vercel Edge',
        provider: 'vercel-edge',
        region: 'unknown',
        error: data.error || 'Unknown error',
        requestTime,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('Vercel Edge Function 调用失败:', error);
    results.push({
      success: false,
      name: 'Vercel Edge',
      provider: 'vercel-edge',
      region: 'unknown',
      error: error instanceof Error ? error.message : 'Network error',
      timestamp: new Date().toISOString()
    });
  }
  
  return results;
}

// 生成基于地理位置的智能模拟数据
function generateVercelSimulatedData(target: string): any[] {
  const targetLower = target.toLowerCase();
  const isChineseSite = targetLower.includes('baidu') || targetLower.includes('qq') || 
                       targetLower.includes('taobao') || targetLower.includes('163') ||
                       targetLower.includes('sina') || targetLower.includes('weibo') ||
                       targetLower.includes('bilibili') || targetLower.includes('tencent');
  
  // 基于 Vercel Edge 真实网络延迟的模拟数据
  const simulatedNodes = [
    { name: '香港', region: 'hkg1', baseLatency: isChineseSite ? 12 : 45 },
    { name: '新加坡', region: 'sin1', baseLatency: isChineseSite ? 25 : 60 },
    { name: '东京', region: 'nrt1', baseLatency: isChineseSite ? 35 : 80 },
    { name: '首尔', region: 'icn1', baseLatency: isChineseSite ? 30 : 70 },
    { name: '旧金山', region: 'sfo1', baseLatency: isChineseSite ? 180 : 120 },
    { name: '华盛顿', region: 'iad1', baseLatency: isChineseSite ? 200 : 140 },
    { name: '法兰克福', region: 'fra1', baseLatency: isChineseSite ? 220 : 160 },
    { name: '伦敦', region: 'lhr1', baseLatency: isChineseSite ? 240 : 180 }
  ];
  
  return simulatedNodes.map(node => ({
    success: true,
    name: node.name,
    provider: 'vercel-edge-simulation',
    region: node.region,
    latency: Math.round(node.baseLatency + Math.random() * 15),
    testMethod: 'Vercel-Edge-Intelligent-Simulation',
    timestamp: new Date().toISOString(),
    isSimulated: true
  }));
}

export async function POST(request: NextRequest) {
  try {
    const { target } = await request.json();
    
    if (!target) {
      return NextResponse.json(
        { error: 'Target URL is required' },
        { status: 400 }
      );
    }
    
    console.log(`🌐 开始 Vercel Edge Functions ping测试: ${target}`);
    
    // 调用 Vercel Edge Function
    const vercelResults = await callVercelEdgeFunction(target);
    
    // 如果真实测试失败，使用智能模拟数据
    let finalResults = vercelResults;
    if (vercelResults.length === 0 || !vercelResults.some(r => r.success)) {
      console.log('⚠️ Vercel Edge Function 测试失败，使用智能模拟数据');
      const simulatedData = generateVercelSimulatedData(target);
      finalResults = [...vercelResults, ...simulatedData];
    }
    
    // 计算统计信息
    const successfulResults = finalResults.filter(r => r.success);
    const realResults = finalResults.filter(r => !r.isSimulated);
    const simulatedResults = finalResults.filter(r => r.isSimulated);
    
    const response = {
      success: true,
      target,
      timestamp: new Date().toISOString(),
      provider: 'vercel-edge-functions',
      summary: {
        totalNodes: finalResults.length,
        realNodes: realResults.length,
        simulatedNodes: simulatedResults.length,
        successfulNodes: successfulResults.length,
        averageLatency: successfulResults.length > 0 
          ? Math.round(successfulResults.reduce((sum, r) => sum + (r.latency || 0), 0) / successfulResults.length)
          : null
      },
      results: finalResults
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Vercel Edge Functions 测试失败:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        provider: 'vercel-edge-functions'
      },
      { status: 500 }
    );
  }
}
