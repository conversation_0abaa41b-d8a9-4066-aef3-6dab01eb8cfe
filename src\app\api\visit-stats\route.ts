import { NextRequest, NextResponse } from 'next/server';

// 全局访问计数器（不依赖用户标识）
let globalVisitCount = 0;

// 防止短时间内重复计数的简单机制
const recentVisits = new Set<string>();

// 生成访问指纹（基于IP和User-Agent，但不存储个人信息）
function generateVisitFingerprint(request: NextRequest): string {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const timestamp = Math.floor(Date.now() / 60000); // 1分钟内的访问视为同一次
  return `${ip}-${userAgent.slice(0, 50)}-${timestamp}`;
}

// GET - 获取访问统计
export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      data: {
        totalVisits: globalVisitCount,
        userVisits: globalVisitCount, // 现在用户访问次数等于全局访问次数
        userId: 'global' // 不再使用个人标识
      }
    });
  } catch (error) {
    console.error('获取访问统计失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get visit stats' },
      { status: 500 }
    );
  }
}

// POST - 记录访问
export async function POST(request: NextRequest) {
  try {
    // 每次请求都增加访问次数（真正的每次刷新+1）
    globalVisitCount++;

    console.log(`📊 访问计数: ${globalVisitCount}`);

    return NextResponse.json({
      success: true,
      data: {
        totalVisits: globalVisitCount,
        userVisits: globalVisitCount,
        userId: 'global'
      }
    });
  } catch (error) {
    console.error('记录访问失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to record visit' },
      { status: 500 }
    );
  }
}

// DELETE - 重置全局访问计数（管理员功能）
export async function DELETE(request: NextRequest) {
  try {
    // 重置全局访问次数为1
    globalVisitCount = 1;

    // 清空最近访问记录
    recentVisits.clear();

    return NextResponse.json({
      success: true,
      data: {
        totalVisits: globalVisitCount,
        userVisits: globalVisitCount,
        userId: 'global'
      }
    });
  } catch (error) {
    console.error('重置访问计数失败:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to reset visit count' },
      { status: 500 }
    );
  }
}
