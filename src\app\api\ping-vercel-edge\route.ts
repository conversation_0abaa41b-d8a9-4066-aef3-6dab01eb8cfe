// Vercel Edge Functions API 路由
// 调用部署在 /api/ping-vercel-edge.js 的边缘函数

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { target } = await request.json();
    
    if (!target) {
      return NextResponse.json(
        { error: '缺少目标URL参数' },
        { status: 400 }
      );
    }

    // 验证URL格式
    try {
      new URL(target);
    } catch (e) {
      return NextResponse.json(
        { error: '无效的URL格式', target },
        { status: 400 }
      );
    }

    // 调用Vercel Edge Function
    const edgeUrl = new URL('/api/edge-ping', request.url);
    edgeUrl.searchParams.set('target', target);
    
    const response = await fetch(edgeUrl.toString(), {
      method: 'GET',
      headers: {
        'User-Agent': 'Ping-Tool-Internal/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`Edge Function 响应错误: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      service: 'Vercel Edge Functions',
      ...data
    });

  } catch (error) {
    console.error('Vercel Edge Functions API 错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      service: 'Vercel Edge Functions'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const target = searchParams.get('target');
  
  if (!target) {
    return NextResponse.json(
      { error: '缺少目标URL参数' },
      { status: 400 }
    );
  }

  // 转发到POST处理器
  return POST(new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify({ target })
  }));
}
