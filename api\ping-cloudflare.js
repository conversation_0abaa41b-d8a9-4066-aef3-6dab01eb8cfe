// Cloudflare Workers - 全球网络延迟测试
// 部署到 Cloudflare 全球边缘网络，提供超快速的网络延迟测试

export default {
  async fetch(request, env, ctx) {
    // 处理 CORS
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };

    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      const url = new URL(request.url);
      const targetUrl = url.searchParams.get('url');
      
      if (!targetUrl) {
        return new Response(JSON.stringify({ 
          error: '缺少目标URL参数' 
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // 获取 Cloudflare 边缘位置信息
      const cf = request.cf || {};
      const edgeLocation = {
        colo: cf.colo || 'Unknown', // Cloudflare 数据中心代码
        country: cf.country || 'Unknown',
        city: cf.city || 'Unknown',
        region: cf.region || 'Unknown',
        timezone: cf.timezone || 'Unknown',
        latitude: cf.latitude || null,
        longitude: cf.longitude || null
      };

      // 执行网络延迟测试
      const startTime = Date.now();
      
      try {
        // 使用 HEAD 请求测试延迟
        const response = await fetch(targetUrl, {
          method: 'HEAD',
          headers: {
            'User-Agent': 'Cloudflare-Workers-Ping-Test/1.0',
            'Accept': '*/*',
            'Cache-Control': 'no-cache'
          },
          // 设置超时时间
          signal: AbortSignal.timeout(10000) // 10秒超时
        });

        const endTime = Date.now();
        const latency = endTime - startTime;

        // 获取响应头信息
        const responseHeaders = {};
        response.headers.forEach((value, key) => {
          responseHeaders[key] = value;
        });

        // 构建测试结果
        const result = {
          success: true,
          url: targetUrl,
          latency: latency,
          status: response.status,
          statusText: response.statusText,
          timestamp: new Date().toISOString(),
          edgeLocation: edgeLocation,
          responseHeaders: responseHeaders,
          testMethod: 'HEAD',
          provider: 'Cloudflare Workers'
        };

        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

      } catch (fetchError) {
        const endTime = Date.now();
        const latency = endTime - startTime;

        // 处理网络错误
        const result = {
          success: false,
          url: targetUrl,
          latency: latency,
          error: fetchError.message,
          timestamp: new Date().toISOString(),
          edgeLocation: edgeLocation,
          testMethod: 'HEAD',
          provider: 'Cloudflare Workers'
        };

        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

    } catch (error) {
      return new Response(JSON.stringify({ 
        error: '服务器内部错误: ' + error.message 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  }
};

// Cloudflare Workers 配置
export const config = {
  // 这个配置在 wrangler.toml 中设置
  name: 'ping-test-worker',
  compatibility_date: '2024-01-01',
  compatibility_flags: ['nodejs_compat']
};
