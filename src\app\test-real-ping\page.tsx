'use client';

import { useState } from 'react';

export default function TestRealPing() {
  const [target, setTarget] = useState('https://proton.me/');
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testRealPing = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      console.log('🚀 Starting real ping test for:', target);
      
      const response = await fetch('/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target,
          useHybridTest: false,
          useBatchTesting: false,
          maxNodes: 15
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📊 Ping test results:', data);
      
      setResults(data);
    } catch (err) {
      console.error('❌ Ping test error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
            🌐 真实多云Ping测试
          </h1>
          
          <div className="mb-8">
            <div className="flex gap-4 items-center">
              <input
                type="text"
                value={target}
                onChange={(e) => setTarget(e.target.value)}
                placeholder="输入要测试的URL..."
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={testRealPing}
                disabled={loading || !target}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {loading ? '测试中...' : '开始测试'}
              </button>
            </div>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">❌ 错误: {error}</p>
            </div>
          )}

          {results && (
            <div className="space-y-6">
              {/* 测试元数据 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">📊 测试信息</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">目标</p>
                    <p className="font-medium">{results.metadata?.target}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">测试类型</p>
                    <p className="font-medium">{results.metadata?.testType}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">数据源</p>
                    <p className="font-medium">{results.metadata?.dataSource || '模拟'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">成功率</p>
                    <p className="font-medium">{results.metadata?.successRate}</p>
                  </div>
                </div>
              </div>

              {/* 测试结果 */}
              <div className="bg-white border rounded-lg overflow-hidden">
                <h2 className="text-xl font-semibold p-6 border-b">🎯 测试结果</h2>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          城市
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          延迟
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          测试方法
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          网络
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {results.results?.map((result: any, index: number) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {result.node}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {result.location?.province}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {result.ping}ms
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              result.status === 'success' 
                                ? 'bg-green-100 text-green-800' 
                                : result.status === 'timeout'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {result.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {result.testMethod || 'Unknown'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {result.location?.network || 'Unknown'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* 原始数据 */}
              <details className="bg-gray-50 rounded-lg p-6">
                <summary className="cursor-pointer text-lg font-semibold mb-4">
                  🔍 原始数据 (点击展开)
                </summary>
                <pre className="bg-white p-4 rounded border overflow-auto text-sm">
                  {JSON.stringify(results, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
