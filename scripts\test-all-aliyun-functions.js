#!/usr/bin/env node

// 测试所有阿里云函数的可用性
console.log('🔍 测试所有阿里云函数可用性');
console.log('=' .repeat(50));

const ALIYUN_FUNCTIONS = [
  {
    name: '北京',
    region: 'cn-beijing',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-beijing/ping-function-beijing/',
    city: '北京'
  },
  {
    name: '上海', 
    region: 'cn-shanghai',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shanghai/ping-function-shanghai/',
    city: '上海'
  },
  {
    name: '深圳',
    region: 'cn-shenzhen', 
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-shenzhen/ping-function-shenzhen/',
    city: '深圳'
  },
  {
    name: '杭州',
    region: 'cn-hangzhou',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-hangzhou/ping-function-hangzhou/',
    city: '杭州'
  },
  {
    name: '青岛',
    region: 'cn-qingdao',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-qingdao/ping-function-qingdao/',
    city: '青岛'
  },
  {
    name: '广州',
    region: 'cn-guangzhou',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-guangzhou/ping-function-guangzhou/',
    city: '广州'
  },
  {
    name: '成都',
    region: 'cn-chengdu',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-chengdu/ping-function-chengdu/',
    city: '成都'
  },
  {
    name: '重庆',
    region: 'cn-chongqing',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-chongqing/ping-function-chongqing/',
    city: '重庆'
  },
  {
    name: '南京',
    region: 'cn-nanjing',
    url: 'https://1322152058828090.cn-beijing.fc.aliyuncs.com/2016-08-15/proxy/ping-service-nanjing/ping-function-nanjing/',
    city: '南京'
  }
];

// 测试单个函数
async function testFunction(func) {
  console.log(`\n🔍 测试 ${func.name} 节点...`);
  console.log(`   📍 地区: ${func.region}`);
  console.log(`   🔗 URL: ${func.url}`);
  
  const startTime = Date.now();
  
  try {
    const response = await fetch(func.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target: 'https://www.baidu.com'
      }),
      signal: AbortSignal.timeout(10000) // 10秒超时
    });
    
    const requestTime = Date.now() - startTime;
    console.log(`   ⏱️ 请求时间: ${requestTime}ms`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ 函数执行成功`);
      console.log(`   📊 测试延迟: ${data.latency}ms`);
      console.log(`   🎯 测试成功: ${data.success}`);
      console.log(`   📍 返回城市: ${data.city}`);
      
      return {
        success: true,
        latency: data.latency,
        requestTime: requestTime,
        city: data.city
      };
    } else {
      console.log(`   ❌ 函数执行失败: HTTP ${response.status}`);
      return {
        success: false,
        error: `HTTP ${response.status}`,
        requestTime: requestTime
      };
    }
    
  } catch (error) {
    const requestTime = Date.now() - startTime;
    console.log(`   ❌ 连接失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      requestTime: requestTime
    };
  }
}

// 批量测试所有函数
async function testAllFunctions() {
  console.log('🚀 开始测试所有阿里云函数...');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const func of ALIYUN_FUNCTIONS) {
    const result = await testFunction(func);
    results.push({
      ...func,
      ...result
    });
    
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 生成测试报告
  console.log('\n📊 测试报告汇总');
  console.log('=' .repeat(60));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`✅ 成功函数: ${successCount}/${totalCount}`);
  console.log(`📈 成功率: ${(successCount / totalCount * 100).toFixed(1)}%`);
  
  console.log('\n📋 详细结果:');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const latency = result.success ? `${result.latency}ms` : result.error;
    console.log(`${index + 1}. ${status} ${result.name}: ${latency} (请求${result.requestTime}ms)`);
  });
  
  // 生成环境变量配置
  console.log('\n🔧 Vercel环境变量配置:');
  console.log('=' .repeat(60));
  
  const successfulFunctions = results.filter(r => r.success);
  if (successfulFunctions.length > 0) {
    successfulFunctions.forEach(func => {
      const envName = `ALIYUN_${func.name.toUpperCase()}_ENDPOINT`;
      console.log(`${envName}=${func.url}`);
    });
  } else {
    console.log('❌ 没有可用的函数');
  }
  
  console.log('\n🎉 测试完成！');
}

// 执行测试
testAllFunctions().catch(console.error);
