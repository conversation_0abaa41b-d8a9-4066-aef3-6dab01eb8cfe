// Cloudflare Workers - 全球网络延迟测试
// 部署到Cloudflare全球200+节点，提供最快的网络延迟测试

// 获取客户端地理位置信息
function getLocationInfo(request) {
  const cf = request.cf || {};
  
  return {
    country: cf.country || 'Unknown',
    region: cf.region || 'Unknown', 
    city: cf.city || 'Unknown',
    timezone: cf.timezone || 'UTC',
    latitude: cf.latitude || 0,
    longitude: cf.longitude || 0,
    colo: cf.colo || 'Unknown', // Cloudflare数据中心代码
    asn: cf.asn || 0,
    asOrganization: cf.asOrganization || 'Unknown'
  };
}

// 执行HTTP延迟测试
async function performHttpLatencyTest(target, timeout = 5000) {
  const startTime = Date.now();
  
  try {
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(target, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Cloudflare-Worker-Ping-Test/1.0',
        'Accept': '*/*',
        'Cache-Control': 'no-cache'
      },
      cf: {
        // Cloudflare特定选项
        cacheTtl: 0, // 不缓存
        cacheEverything: false
      }
    });
    
    clearTimeout(timeoutId);
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    return {
      success: true,
      latency,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    };
    
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    return {
      success: false,
      latency,
      error: error.name === 'AbortError' ? 'Timeout' : error.message,
      errorType: error.name
    };
  }
}

// 执行多次测试并计算统计数据
async function performMultipleTests(target, testCount = 3) {
  const results = [];
  
  for (let i = 0; i < testCount; i++) {
    const result = await performHttpLatencyTest(target);
    results.push(result);
    
    // 测试间隔50ms (Cloudflare Workers响应更快)
    if (i < testCount - 1) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  // 分析结果
  const successfulTests = results.filter(r => r.success);
  const latencies = successfulTests.map(r => r.latency);
  
  if (latencies.length === 0) {
    return {
      success: false,
      error: 'All tests failed',
      results,
      testCount: results.length
    };
  }
  
  // 计算统计数据
  latencies.sort((a, b) => a - b);
  const sum = latencies.reduce((a, b) => a + b, 0);
  
  return {
    success: true,
    averageLatency: Math.round(sum / latencies.length),
    minLatency: latencies[0],
    maxLatency: latencies[latencies.length - 1],
    medianLatency: latencies[Math.floor(latencies.length / 2)],
    standardDeviation: Math.round(Math.sqrt(
      latencies.reduce((sq, n) => sq + Math.pow(n - (sum / latencies.length), 2), 0) / latencies.length
    )),
    successRate: Math.round((successfulTests.length / results.length) * 100),
    testCount: results.length,
    results
  };
}

// 判断是否为中国网站 - 扩展版本
function isChineseSite(url) {
  const chineseDomains = [
    // 搜索引擎
    'baidu.com', 'so.com', 'sogou.com', 'soso.com',
    // 社交媒体
    'qq.com', 'weibo.com', 'douyin.com', 'tiktok.com', 'wechat.com',
    // 电商平台
    'taobao.com', 'tmall.com', 'jd.com', 'pinduoduo.com', 'alipay.com',
    // 门户网站
    '163.com', 'sina.com.cn', 'sohu.com', 'ifeng.com',
    // 视频娱乐
    'bilibili.com', 'youku.com', 'iqiyi.com', 'tencent.com',
    // 科技公司
    'alibaba.com', 'meituan.com', 'dianping.com', 'ctrip.com',
    // 知识社区
    'zhihu.com', 'csdn.net', 'cnblogs.com', 'oschina.net',
    // 政府机构
    'gov.cn', 'edu.cn', 'org.cn', 'com.cn', 'net.cn'
  ];

  const urlLower = url.toLowerCase();
  return chineseDomains.some(domain => urlLower.includes(domain));
}

// 主处理函数
async function handleRequest(request) {
  // 处理CORS预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Max-Age': '86400'
      }
    });
  }
  
  // 只允许POST请求
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({
      error: 'Method not allowed',
      allowedMethods: ['POST']
    }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
  
  try {
    // 解析请求体
    const body = await request.json();
    const { target, testCount = 3 } = body;
    
    if (!target) {
      return new Response(JSON.stringify({
        error: 'Target URL is required',
        example: { target: 'https://example.com', testCount: 3 }
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
    
    // 验证URL格式
    try {
      new URL(target);
    } catch {
      return new Response(JSON.stringify({
        error: 'Invalid URL format'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
    
    // 获取地理位置信息
    const location = getLocationInfo(request);
    
    // 执行延迟测试
    const testResult = await performMultipleTests(target, Math.min(testCount, 5));
    
    // 构建响应
    const response = {
      success: testResult.success,
      target,
      location,
      timestamp: new Date().toISOString(),
      provider: 'cloudflare-workers',
      dataCenter: location.colo,
      testMethod: 'Cloudflare-Workers-HTTP-HEAD',
      isChineseSite: isChineseSite(target),
      ...testResult
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Powered-By': 'Cloudflare-Workers'
      }
    });
    
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

// 导出事件监听器
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});
