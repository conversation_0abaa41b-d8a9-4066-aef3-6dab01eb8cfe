'use client';

import React, { useState } from 'react';

interface TestResult {
  service: string;
  success: boolean;
  latency?: number;
  error?: string;
  details?: any;
  timestamp: string;
}

export default function TestMultiCloudPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [testTarget, setTestTarget] = useState('https://baidu.com');

  const testServices = async () => {
    setIsRunning(true);
    setResults([]);

    const services = [
      {
        name: 'Vercel Edge Functions',
        endpoint: '/api/ping-vercel-edge',
        color: 'bg-blue-500'
      },
      {
        name: 'Cloudflare Workers',
        endpoint: '/api/ping-cloudflare-worker',
        color: 'bg-orange-500'
      }
    ];

    for (const service of services) {
      try {
        const startTime = Date.now();
        
        const response = await fetch(service.endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ target: testTarget }),
        });

        const endTime = Date.now();
        const data = await response.json();

        const result: TestResult = {
          service: service.name,
          success: response.ok && data.success,
          latency: data.latency,
          error: data.error,
          details: data,
          timestamp: new Date().toISOString()
        };

        setResults(prev => [...prev, result]);

      } catch (error) {
        const result: TestResult = {
          service: service.name,
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          timestamp: new Date().toISOString()
        };

        setResults(prev => [...prev, result]);
      }

      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    setIsRunning(false);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          🌐 多云部署测试页面
        </h1>

        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">测试配置</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              测试目标 URL:
            </label>
            <input
              type="url"
              value={testTarget}
              onChange={(e) => setTestTarget(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="https://example.com"
            />
          </div>

          <button
            onClick={testServices}
            disabled={isRunning || !testTarget.trim()}
            className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
              isRunning || !testTarget.trim()
                ? 'bg-gray-600 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isRunning ? '🔄 测试中...' : '🚀 开始测试'}
          </button>
        </div>

        {results.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">测试结果</h2>
            
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-l-4 ${
                    result.success
                      ? 'bg-green-900/20 border-green-500'
                      : 'bg-red-900/20 border-red-500'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-lg">
                      {result.success ? '✅' : '❌'} {result.service}
                    </h3>
                    <span className="text-sm text-gray-400">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>

                  {result.success ? (
                    <div className="space-y-2">
                      <p className="text-green-400">
                        延迟: <span className="font-mono">{result.latency}ms</span>
                      </p>
                      
                      {result.details?.edge && (
                        <p className="text-blue-400">
                          边缘节点: {result.details.edge.region} ({result.details.edge.country})
                        </p>
                      )}
                      
                      {result.details?.cloudflare && (
                        <p className="text-orange-400">
                          CF数据中心: {result.details.cloudflare.datacenter} ({result.details.cloudflare.colo})
                        </p>
                      )}
                      
                      <details className="mt-2">
                        <summary className="cursor-pointer text-gray-400 hover:text-white">
                          查看详细信息
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-900 rounded text-xs overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    </div>
                  ) : (
                    <p className="text-red-400">
                      错误: {result.error}
                    </p>
                  )}
                </div>
              ))}
            </div>

            {results.length === 2 && (
              <div className="mt-6 p-4 bg-gray-700 rounded-lg">
                <h3 className="font-semibold mb-2">📊 测试总结</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">成功率:</span>
                    <span className="ml-2 font-mono">
                      {results.filter(r => r.success).length}/{results.length}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">平均延迟:</span>
                    <span className="ml-2 font-mono">
                      {results.filter(r => r.success && r.latency)
                        .reduce((sum, r, _, arr) => sum + (r.latency! / arr.length), 0)
                        .toFixed(1)}ms
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">📋 部署状态</h2>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
              <span>🇭🇰 Vercel Edge Functions (香港)</span>
              <span className="px-2 py-1 bg-blue-600 rounded text-xs">
                免费用户限制
              </span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
              <span>🌐 Cloudflare Workers (全球)</span>
              <span className="px-2 py-1 bg-green-600 rounded text-xs">
                已部署
              </span>
            </div>
          </div>

          <div className="mt-4 text-sm text-gray-400">
            <p>• Vercel Edge Functions: 优先亚太地区，香港节点</p>
            <p>• Cloudflare Workers: 支持中国大陆及全球节点</p>
            <p>• 智能路由: 根据目标网站自动选择最优服务</p>
          </div>
        </div>
      </div>
    </div>
  );
}
