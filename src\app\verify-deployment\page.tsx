'use client';

import React, { useState } from 'react';

interface TestResult {
  service: string;
  success: boolean;
  latency?: number;
  error?: string;
  region?: string;
  timestamp: string;
}

export default function VerifyDeploymentPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const runQuickTest = async () => {
    setIsRunning(true);
    setResults([]);

    const testUrl = 'https://baidu.com';

    // 测试 Cloudflare Workers
    try {
      const cfResponse = await fetch('/api/ping-cloudflare-worker', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: testUrl }),
      });

      const cfData = await cfResponse.json();
      
      setResults(prev => [...prev, {
        service: 'Cloudflare Workers',
        success: cfResponse.ok && cfData.success,
        latency: cfData.latency,
        error: cfData.error,
        region: cfData.cloudflare?.datacenter || 'Unknown',
        timestamp: new Date().toISOString()
      }]);

    } catch (error) {
      setResults(prev => [...prev, {
        service: 'Cloudflare Workers',
        success: false,
        error: error instanceof Error ? error.message : '网络错误',
        timestamp: new Date().toISOString()
      }]);
    }

    // 测试 Vercel Edge Functions
    try {
      const vercelResponse = await fetch('/api/ping-vercel-edge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: testUrl }),
      });

      const vercelData = await vercelResponse.json();
      
      setResults(prev => [...prev, {
        service: 'Vercel Edge Functions',
        success: vercelResponse.ok && vercelData.success,
        latency: vercelData.latency,
        error: vercelData.error,
        region: vercelData.edge?.region || 'Unknown',
        timestamp: new Date().toISOString()
      }]);

    } catch (error) {
      setResults(prev => [...prev, {
        service: 'Vercel Edge Functions',
        success: false,
        error: error instanceof Error ? error.message : '网络错误',
        timestamp: new Date().toISOString()
      }]);
    }

    setIsRunning(false);
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-400' : 'text-red-400';
  };

  const getStatusIcon = (success: boolean) => {
    return success ? '✅' : '❌';
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          🚀 多云部署验证
        </h1>

        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">快速验证测试</h2>
          <p className="text-gray-400 mb-4">
            测试 Vercel Edge Functions 和 Cloudflare Workers 是否正常工作
          </p>
          
          <button
            onClick={runQuickTest}
            disabled={isRunning}
            className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
              isRunning
                ? 'bg-gray-600 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isRunning ? '🔄 测试中...' : '🧪 开始快速测试'}
          </button>
        </div>

        {results.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">测试结果</h2>
            
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-l-4 ${
                    result.success
                      ? 'bg-green-900/20 border-green-500'
                      : 'bg-red-900/20 border-red-500'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-lg">
                      {getStatusIcon(result.success)} {result.service}
                    </h3>
                    <span className="text-sm text-gray-400">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>

                  {result.success ? (
                    <div className="space-y-1">
                      <p className={getStatusColor(result.success)}>
                        ✓ 服务正常运行
                      </p>
                      {result.latency && (
                        <p className="text-blue-400">
                          延迟: <span className="font-mono">{result.latency}ms</span>
                        </p>
                      )}
                      {result.region && (
                        <p className="text-purple-400">
                          节点: {result.region}
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className={getStatusColor(result.success)}>
                      ✗ 错误: {result.error}
                    </p>
                  )}
                </div>
              ))}
            </div>

            {results.length === 2 && (
              <div className="mt-6 p-4 bg-gray-700 rounded-lg">
                <h3 className="font-semibold mb-2">📊 部署状态总结</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">成功服务:</span>
                    <span className="ml-2 font-mono text-green-400">
                      {results.filter(r => r.success).length}/{results.length}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">部署状态:</span>
                    <span className={`ml-2 font-mono ${
                      results.every(r => r.success) ? 'text-green-400' : 
                      results.some(r => r.success) ? 'text-yellow-400' : 'text-red-400'
                    }`}>
                      {results.every(r => r.success) ? '✅ 完全成功' : 
                       results.some(r => r.success) ? '⚠️ 部分成功' : '❌ 部署失败'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">🔧 部署信息</h2>
          
          <div className="space-y-4">
            <div className="border border-gray-700 rounded-lg p-4">
              <h3 className="font-semibold text-blue-400 mb-2">
                🇭🇰 Vercel Edge Functions
              </h3>
              <div className="text-sm space-y-1">
                <p><span className="text-gray-400">支持区域:</span> 香港, 首尔, 东京, 新加坡, 悉尼, 孟买</p>
                <p><span className="text-gray-400">免费限制:</span> 只能选择一个区域 (推荐香港)</p>
                <p><span className="text-gray-400">API端点:</span> /api/ping-vercel-edge</p>
              </div>
            </div>

            <div className="border border-gray-700 rounded-lg p-4">
              <h3 className="font-semibold text-orange-400 mb-2">
                🌐 Cloudflare Workers
              </h3>
              <div className="text-sm space-y-1">
                <p><span className="text-gray-400">支持区域:</span> 中国大陆 + 全球57个节点</p>
                <p><span className="text-gray-400">部署状态:</span> ✅ 已部署</p>
                <p><span className="text-gray-400">Worker URL:</span> ping-network-test.wob21.workers.dev</p>
                <p><span className="text-gray-400">API端点:</span> /api/ping-cloudflare-worker</p>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
            <p className="text-sm text-blue-300">
              💡 <strong>提示:</strong> 如果Vercel Edge Functions测试失败，可能是因为正在重新部署。
              请等待几分钟后重试。Cloudflare Workers应该始终可用。
            </p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a
            href="/"
            className="inline-block px-6 py-3 bg-green-600 hover:bg-green-700 rounded-lg font-medium transition-colors"
          >
            🏠 返回主页面
          </a>
        </div>
      </div>
    </div>
  );
}
