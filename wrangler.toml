# 🛰️ Cloudflare Workers 配置文件 - 全球网络延迟测试
name = "ping-network-test"
main = "api/ping-cloudflare-worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 🚀 智能部署模式
[placement]
mode = "smart" # 自动部署到最近的数据中心（适合低延迟场景）

# 🏗️ 生产环境配置
[env.production]
name = "ping-network-test"

[env.production.vars]
ENVIRONMENT = "production"
DEBUG = "false"

# 🔧 开发环境配置
[env.development]
name = "ping-network-test-dev"

[env.development.vars]
ENVIRONMENT = "development"
DEBUG = "true"

# 🌐 环境变量配置（中国大陆优先节点 + 全局支持）
[vars]
SERVICE_NAME = "Ping Test Worker"
VERSION = "1.0.0"
ENVIRONMENT = "production"
CHINA_MAINLAND_SUPPORT = "true"
MAX_TIMEOUT = "8000"
DEFAULT_TIMEOUT = "5000"
SUPPORTED_REGIONS = "SHA,BJS,CAN,NKG,WUH,XMN,HGH,CSX,HRB,CGO,TAO,SJW,TYN,LHW,KWE,NNG,KMG,HFE,FOC,HET,SHE,DLC,TSN,JZH,XNN,URC,YCU,ZUH,HKG,MFM,TPE,ICN,NRT,SIN,KUL,BKK,MNL,SYD,BOM,LAX,SFO,SEA,DEN,ORD,IAD,JFK,YYZ,LHR,FRA,AMS,ARN,DME,CAI,JNB,GRU,SCL"

# ⏰ 定时触发器（健康检查或统计任务）
[[triggers]]
crons = ["0 */6 * * *"] # 每 6 小时执行一次

# 🗄️ KV 存储空间（如需开启缓存功能）
# [[kv_namespaces]]
# binding = "PING_CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# 📦 Durable Objects / R2 / D1 等（如需集成持久化服务）
