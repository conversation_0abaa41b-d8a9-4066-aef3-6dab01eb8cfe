# Cloudflare Workers 配置文件
name = "ping-network-test"
main = "cloudflare-workers/ping-worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 生产环境配置
[env.production]
name = "ping-network-test"

# 开发环境配置  
[env.development]
name = "ping-network-test-dev"

# 变量配置
[vars]
ENVIRONMENT = "production"
VERSION = "1.0.0"

# 触发器配置
[[triggers]]
crons = ["0 */6 * * *"]  # 每6小时执行一次健康检查

# KV 存储配置 (可选，用于缓存)
# [[kv_namespaces]]
# binding = "PING_CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"
