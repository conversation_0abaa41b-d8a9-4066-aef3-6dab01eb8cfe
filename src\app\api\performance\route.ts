import { NextRequest, NextResponse } from 'next/server';
import { RateLimiter } from '../../../utils/ValidationUtils';

// 性能数据存储（生产环境应该使用数据库）
const performanceData: any[] = [];
const MAX_RECORDS = 10000;

// 速率限制器
const performanceLimiter = RateLimiter.getInstance('performance-reports', 20, 60000); // 每分钟最多20个报告

// POST - 接收性能报告
export async function POST(request: NextRequest) {
  try {
    // 获取客户端IP
    const ip = request.ip || 
               request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // 检查速率限制
    const rateLimitResult = performanceLimiter.checkLimit(ip);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Too many performance reports', 
          resetTime: rateLimitResult.resetTime 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': '20',
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
          }
        }
      );
    }

    const performanceReport = await request.json();

    // 验证性能数据
    if (!performanceReport.sessionId || !performanceReport.metrics) {
      return NextResponse.json(
        { error: 'Invalid performance data' },
        { status: 400 }
      );
    }

    // 清理和验证指标数据
    const sanitizedReport = {
      sessionId: sanitizeString(performanceReport.sessionId),
      timestamp: performanceReport.timestamp || Date.now(),
      metrics: sanitizeMetrics(performanceReport.metrics),
      userAgent: sanitizeString(performanceReport.userAgent),
      url: sanitizeString(performanceReport.url),
      viewport: performanceReport.viewport,
      connection: performanceReport.connection,
      ip: ip.substring(0, ip.lastIndexOf('.')) + '.xxx' // 部分隐藏IP
    };

    // 存储性能数据
    performanceData.push(sanitizedReport);
    
    // 保持数据量在限制内
    if (performanceData.length > MAX_RECORDS) {
      performanceData.splice(0, performanceData.length - MAX_RECORDS);
    }

    // 分析性能数据并生成告警
    analyzePerformanceData(sanitizedReport);

    return NextResponse.json(
      { 
        success: true, 
        reportId: sanitizedReport.sessionId 
      },
      {
        headers: {
          'X-RateLimit-Limit': '20',
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      }
    );

  } catch (error) {
    console.error('Error handling performance report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - 获取性能统计（仅开发环境）
export async function GET(request: NextRequest) {
  // 仅在开发环境提供性能统计
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Not available in production' },
      { status: 403 }
    );
  }

  const url = new URL(request.url);
  const timeRange = parseInt(url.searchParams.get('timeRange') || '3600000'); // 默认1小时
  const metric = url.searchParams.get('metric');

  const now = Date.now();
  const cutoffTime = now - timeRange;

  // 过滤时间范围内的数据
  const recentData = performanceData.filter(report => report.timestamp > cutoffTime);

  // 生成统计信息
  const stats = generatePerformanceStats(recentData, metric);

  return NextResponse.json(stats);
}

// 工具函数
function sanitizeString(str: string | undefined): string {
  if (!str || typeof str !== 'string') return '';
  return str.substring(0, 1000); // 限制长度
}

function sanitizeMetrics(metrics: any[]): any[] {
  if (!Array.isArray(metrics)) return [];
  
  return metrics
    .filter(metric => 
      metric && 
      typeof metric.name === 'string' && 
      typeof metric.value === 'number' &&
      typeof metric.timestamp === 'number'
    )
    .slice(0, 100) // 限制指标数量
    .map(metric => ({
      name: sanitizeString(metric.name),
      value: Math.max(0, Math.min(metric.value, 1000000)), // 限制值范围
      unit: sanitizeString(metric.unit),
      timestamp: metric.timestamp,
      category: sanitizeString(metric.category),
      tags: sanitizeTags(metric.tags)
    }));
}

function sanitizeTags(tags: any): Record<string, string> {
  if (!tags || typeof tags !== 'object') return {};
  
  const sanitized: Record<string, string> = {};
  let count = 0;
  
  for (const [key, value] of Object.entries(tags)) {
    if (count >= 10) break; // 限制标签数量
    if (typeof key === 'string' && typeof value === 'string') {
      sanitized[sanitizeString(key)] = sanitizeString(value);
      count++;
    }
  }
  
  return sanitized;
}

function analyzePerformanceData(report: any): void {
  const metrics = report.metrics;
  
  // 检查页面加载性能
  const pageLoadMetric = metrics.find((m: any) => m.name === 'page_load_time');
  if (pageLoadMetric && pageLoadMetric.value > 5000) {
    console.warn('🐌 Slow page load detected:', {
      sessionId: report.sessionId,
      loadTime: pageLoadMetric.value,
      url: report.url
    });
  }

  // 检查长任务
  const longTasks = metrics.filter((m: any) => m.name === 'long_task');
  if (longTasks.length > 5) {
    console.warn('⚠️ Multiple long tasks detected:', {
      sessionId: report.sessionId,
      taskCount: longTasks.length,
      url: report.url
    });
  }

  // 检查布局偏移
  const clsMetric = metrics.find((m: any) => m.name === 'cumulative_layout_shift');
  if (clsMetric && clsMetric.value > 0.25) {
    console.warn('📐 High layout shift detected:', {
      sessionId: report.sessionId,
      clsScore: clsMetric.value,
      url: report.url
    });
  }

  // 检查首次输入延迟
  const fidMetric = metrics.find((m: any) => m.name === 'first_input_delay');
  if (fidMetric && fidMetric.value > 300) {
    console.warn('🖱️ High input delay detected:', {
      sessionId: report.sessionId,
      inputDelay: fidMetric.value,
      url: report.url
    });
  }
}

function generatePerformanceStats(data: any[], metricFilter?: string | null): any {
  if (data.length === 0) {
    return {
      totalReports: 0,
      timeRange: 0,
      metrics: {},
      summary: {}
    };
  }

  const allMetrics = data.flatMap(report => report.metrics);
  const filteredMetrics = metricFilter 
    ? allMetrics.filter(m => m.name === metricFilter)
    : allMetrics;

  // 按指标名称分组
  const metricGroups: Record<string, any[]> = {};
  filteredMetrics.forEach(metric => {
    if (!metricGroups[metric.name]) {
      metricGroups[metric.name] = [];
    }
    metricGroups[metric.name].push(metric);
  });

  // 计算每个指标的统计信息
  const metricStats: Record<string, any> = {};
  for (const [name, metrics] of Object.entries(metricGroups)) {
    const values = metrics.map(m => m.value);
    metricStats[name] = {
      count: values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((sum, val) => sum + val, 0) / values.length,
      p50: percentile(values, 0.5),
      p90: percentile(values, 0.9),
      p95: percentile(values, 0.95),
      p99: percentile(values, 0.99)
    };
  }

  // 生成摘要
  const summary = {
    totalSessions: new Set(data.map(r => r.sessionId)).size,
    avgPageLoad: metricStats['page_load_time']?.avg || 0,
    avgNetworkLatency: metricStats['time_to_first_byte']?.avg || 0,
    longTaskCount: metricStats['long_task']?.count || 0,
    avgCLS: metricStats['cumulative_layout_shift']?.avg || 0,
    avgFID: metricStats['first_input_delay']?.avg || 0
  };

  return {
    totalReports: data.length,
    timeRange: data.length > 0 ? data[data.length - 1].timestamp - data[0].timestamp : 0,
    metrics: metricStats,
    summary
  };
}

function percentile(values: number[], p: number): number {
  const sorted = values.slice().sort((a, b) => a - b);
  const index = Math.ceil(sorted.length * p) - 1;
  return sorted[Math.max(0, index)];
}
