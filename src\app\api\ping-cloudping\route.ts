import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { target } = body;

    if (!target) {
      return NextResponse.json(
        { error: "缺少目标URL参数" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      results: [{
        node: "香港",
        ping: 50 + Math.random() * 50,
        status: "success",
        timestamp: Date.now(),
        location: {
          country: "Hong Kong",
          city: "香港",
          province: "香港",
          region: "港澳台",
          latitude: 22.3193,
          longitude: 114.1694,
          asn: 0,
          network: "Mock Data"
        },
        testMethod: "Mock Simulation",
        testEndpoint: target,
        attempts: 1
      }],
      target,
      timestamp: new Date().toISOString(),
      metadata: {
        totalNodes: 1,
        successfulNodes: 1,
        testMethod: "Mock Simulation"
      }
    });

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "未知错误",
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    service: "CloudPing API",
    version: "1.0.0",
    status: "active",
    description: "简化的多云ping测试API",
    timestamp: new Date().toISOString()
  });
}
