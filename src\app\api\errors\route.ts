import { NextRequest, NextResponse } from 'next/server';
import { RateLimiter } from '../../../utils/ValidationUtils';

// 错误日志存储（生产环境应该使用数据库或外部服务）
const errorLogs: any[] = [];
const MAX_LOGS = 1000;

// 速率限制器
const errorReportLimiter = RateLimiter.getInstance('error-reports', 5, 60000); // 每分钟最多5个错误报告

// POST - 接收错误报告
export async function POST(request: NextRequest) {
  try {
    // 获取客户端IP
    const ip = request.ip || 
               request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // 检查速率限制
    const rateLimitResult = errorReportLimiter.checkLimit(ip);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: 'Too many error reports', 
          resetTime: rateLimitResult.resetTime 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': '5',
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
          }
        }
      );
    }

    const errorData = await request.json();

    // 验证错误数据
    if (!errorData.message || !errorData.timestamp) {
      return NextResponse.json(
        { error: 'Invalid error data' },
        { status: 400 }
      );
    }

    // 清理敏感信息
    const sanitizedError = {
      id: errorData.id || generateErrorId(),
      message: sanitizeString(errorData.message),
      stack: sanitizeString(errorData.stack),
      componentStack: sanitizeString(errorData.componentStack),
      timestamp: errorData.timestamp,
      userAgent: sanitizeString(errorData.userAgent),
      url: sanitizeString(errorData.url),
      severity: errorData.severity || 'medium',
      ip: ip.substring(0, ip.lastIndexOf('.')) + '.xxx', // 部分隐藏IP
      context: sanitizeContext(errorData.context)
    };

    // 存储错误日志
    errorLogs.push(sanitizedError);
    
    // 保持日志数量在限制内
    if (errorLogs.length > MAX_LOGS) {
      errorLogs.splice(0, errorLogs.length - MAX_LOGS);
    }

    // 记录到控制台（生产环境可以发送到外部监控服务）
    console.error('Client Error Report:', sanitizedError);

    // 如果是严重错误，可以发送告警
    if (sanitizedError.severity === 'critical') {
      await sendCriticalErrorAlert(sanitizedError);
    }

    return NextResponse.json(
      { 
        success: true, 
        errorId: sanitizedError.id 
      },
      {
        headers: {
          'X-RateLimit-Limit': '5',
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      }
    );

  } catch (error) {
    console.error('Error handling error report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - 获取错误统计（仅开发环境）
export async function GET(request: NextRequest) {
  // 仅在开发环境提供错误统计
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Not available in production' },
      { status: 403 }
    );
  }

  const url = new URL(request.url);
  const limit = parseInt(url.searchParams.get('limit') || '50');
  const severity = url.searchParams.get('severity');

  let filteredLogs = errorLogs;
  
  if (severity) {
    filteredLogs = errorLogs.filter(log => log.severity === severity);
  }

  const recentLogs = filteredLogs
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, Math.min(limit, 100));

  const stats = {
    total: errorLogs.length,
    bySeverity: {
      critical: errorLogs.filter(log => log.severity === 'critical').length,
      high: errorLogs.filter(log => log.severity === 'high').length,
      medium: errorLogs.filter(log => log.severity === 'medium').length,
      low: errorLogs.filter(log => log.severity === 'low').length
    },
    recent: recentLogs
  };

  return NextResponse.json(stats);
}

// 工具函数
function generateErrorId(): string {
  return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function sanitizeString(str: string | undefined): string {
  if (!str || typeof str !== 'string') return '';
  
  // 移除潜在的敏感信息
  return str
    .replace(/password[=:]\s*[^\s&]+/gi, 'password=***')
    .replace(/token[=:]\s*[^\s&]+/gi, 'token=***')
    .replace(/key[=:]\s*[^\s&]+/gi, 'key=***')
    .replace(/secret[=:]\s*[^\s&]+/gi, 'secret=***')
    .substring(0, 5000); // 限制长度
}

function sanitizeContext(context: any): any {
  if (!context || typeof context !== 'object') return {};
  
  const sanitized: any = {};
  
  for (const [key, value] of Object.entries(context)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      sanitized[key] = value;
    } else if (Array.isArray(value)) {
      sanitized[key] = value.slice(0, 10); // 限制数组长度
    } else {
      sanitized[key] = '[Object]';
    }
  }
  
  return sanitized;
}

async function sendCriticalErrorAlert(error: any): Promise<void> {
  // 这里可以集成邮件、Slack、钉钉等告警服务
  console.error('🚨 CRITICAL ERROR ALERT:', {
    id: error.id,
    message: error.message,
    timestamp: new Date(error.timestamp).toISOString(),
    url: error.url
  });
  
  // 示例：发送到Webhook
  try {
    const webhookUrl = process.env.ERROR_WEBHOOK_URL;
    if (webhookUrl) {
      await fetch(webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 Critical Error: ${error.message}`,
          attachments: [{
            color: 'danger',
            fields: [
              { title: 'Error ID', value: error.id, short: true },
              { title: 'URL', value: error.url, short: true },
              { title: 'Time', value: new Date(error.timestamp).toISOString(), short: true }
            ]
          }]
        })
      });
    }
  } catch (webhookError) {
    console.error('Failed to send webhook alert:', webhookError);
  }
}
