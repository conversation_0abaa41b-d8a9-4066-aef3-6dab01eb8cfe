// 共享的Ping测试服务
import tcpp from 'tcp-ping';

// 城市到省份的映射
function getProvinceByCity(city: string): string {
  const cityProvinceMap: Record<string, string> = {
    '北京': '北京',
    '上海': '上海',
    '深圳': '广东',
    '杭州': '浙江',
    '青岛': '山东',
    '广州': '广东',
    '成都': '四川',
    '重庆': '重庆',
    '南京': '江苏'
  };
  return cityProvinceMap[city] || city;
}

// 调用多云API
async function callMultiCloudAPIs(target: string): Promise<any[]> {
  const results = [];
  
  // 阿里云函数调用
  const aliyunRegions = [
    { name: '北京', endpoint: process.env.ALIYUN_BEIJING_ENDPOINT },
    { name: '上海', endpoint: process.env.ALIYUN_SHANGHAI_ENDPOINT },
    { name: '深圳', endpoint: process.env.ALIYUN_SHENZHEN_ENDPOINT },
    { name: '杭州', endpoint: process.env.ALIYUN_HANGZHOU_ENDPOINT },
    { name: '青岛', endpoint: process.env.ALIYUN_QINGDAO_ENDPOINT },
    { name: '广州', endpoint: process.env.ALIYUN_GUANGZHOU_ENDPOINT },
    { name: '成都', endpoint: process.env.ALIYUN_CHENGDU_ENDPOINT },
    { name: '重庆', endpoint: process.env.ALIYUN_CHONGQING_ENDPOINT },
    { name: '南京', endpoint: process.env.ALIYUN_NANJING_ENDPOINT }
  ];

  const promises = aliyunRegions
    .filter(region => region.endpoint)
    .map(async (region) => {
      try {
        const response = await fetch(region.endpoint!, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ target }),
          signal: AbortSignal.timeout(8000)
        });

        if (response.ok) {
          const data = await response.json();
          return {
            node: region.name,
            ping: data.latency || 0,
            status: data.success ? 'success' : 'failed',
            province: getProvinceByCity(region.name)
          };
        }
      } catch (error) {
        console.warn(`阿里云${region.name}节点测试失败:`, error);
      }
      
      return {
        node: region.name,
        ping: 0,
        status: 'timeout',
        province: getProvinceByCity(region.name)
      };
    });

  const aliyunResults = await Promise.allSettled(promises);
  aliyunResults.forEach(result => {
    if (result.status === 'fulfilled' && result.value) {
      results.push(result.value);
    }
  });

  return results;
}

// 注意：已完全移除模拟数据生成，只使用真实的阿里云函数结果

// 主要的ping测试函数
export async function performMultiCloudPing(target: string): Promise<{
  success: boolean;
  results: any[];
  target: string;
}> {
  try {
    console.log(`🌐 开始多云ping测试: ${target}`);

    // 1. 调用真实的阿里云函数
    const realResults = await callMultiCloudAPIs(target);
    console.log(`✅ 真实测试完成，获得 ${realResults.length} 个结果`);

    // 2. 如果没有真实结果，执行简单的HTTP测试作为基准
    let baseLatency = 200; // 默认基准延迟
    if (realResults.length === 0) {
      try {
        console.log('🔍 执行基准HTTP测试...');
        const startTime = Date.now();
        const response = await fetch(target, {
          method: 'HEAD',
          signal: AbortSignal.timeout(5000)
        });
        const endTime = Date.now();
        baseLatency = endTime - startTime;
        console.log(`📊 基准测试完成: ${baseLatency}ms`);

        // 添加一个基准结果
        realResults.push({
          node: 'Vercel Edge',
          ping: baseLatency,
          status: response.ok ? 'success' : 'failed',
          province: '全球'
        });
      } catch (error) {
        console.warn('基准测试失败:', error);
      }
    }

    // 3. 只使用真实的阿里云函数结果
    console.log(`📊 真实节点: ${realResults.length} 个`);

    // 4. 使用真实结果
    const allResults = realResults;

    return {
      success: true,
      results: allResults,
      target
    };
  } catch (error) {
    console.error('多云ping测试失败:', error);

    // 出错时返回空结果
    return {
      success: false,
      results: [],
      target
    };
  }
}
