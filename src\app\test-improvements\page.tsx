'use client';

import { useState, useEffect } from 'react';
import { UptimeRobotService, NetworkMonitoringAggregator } from '../../services/UptimeRobotService';
import { WebRTCLatencyTest, AdvancedNetworkTester } from '../../services/WebRTCLatencyTest';
import { MultiCloudNetworkTester } from '../../services/MultiCloudDeployment';
import { NetworkDataCollector, NetworkLatencyPredictor } from '../../services/NetworkDataCalibration';

export default function TestImprovementsPage() {
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [testTarget, setTestTarget] = useState('https://www.baidu.com');

  // 初始化服务
  const [dataCollector] = useState(() => new NetworkDataCollector());
  const [latencyPredictor] = useState(() => new NetworkLatencyPredictor(dataCollector));
  const [multiCloudTester] = useState(() => new MultiCloudNetworkTester());
  const [advancedTester] = useState(() => new AdvancedNetworkTester());

  // 测试高精度网络测试
  const testAdvancedNetworkTester = async () => {
    setIsLoading(true);
    try {
      console.log('🧪 测试高精度网络测试器...');
      
      const result = await advancedTester.performMultipleTests(testTarget, 5);
      
      setTestResults(prev => ({
        ...prev,
        advancedTester: {
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        }
      }));
      
      console.log('✅ 高精度测试完成:', result);
    } catch (error) {
      console.error('❌ 高精度测试失败:', error);
      setTestResults(prev => ({
        ...prev,
        advancedTester: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // 测试WebRTC延迟测试
  const testWebRTCLatency = async () => {
    setIsLoading(true);
    try {
      console.log('🔗 测试WebRTC延迟测试...');
      
      const webrtcTester = new WebRTCLatencyTest();
      const result = await webrtcTester.performLatencyTest(testTarget);
      
      setTestResults(prev => ({
        ...prev,
        webrtcTester: {
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        }
      }));
      
      console.log('✅ WebRTC测试完成:', result);
      webrtcTester.cleanup();
    } catch (error) {
      console.error('❌ WebRTC测试失败:', error);
      setTestResults(prev => ({
        ...prev,
        webrtcTester: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // 测试多云网络测试器
  const testMultiCloudTester = async () => {
    setIsLoading(true);
    try {
      console.log('☁️ 测试多云网络测试器...');
      
      // 检查节点健康状态
      const healthStatus = await multiCloudTester.checkNodesHealth();
      
      setTestResults(prev => ({
        ...prev,
        multiCloudTester: {
          success: true,
          data: {
            healthStatus,
            totalNodes: healthStatus.length,
            healthyNodes: healthStatus.filter(n => n.healthy).length
          },
          timestamp: new Date().toISOString()
        }
      }));
      
      console.log('✅ 多云测试完成:', healthStatus);
    } catch (error) {
      console.error('❌ 多云测试失败:', error);
      setTestResults(prev => ({
        ...prev,
        multiCloudTester: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // 测试机器学习预测
  const testMLPredictor = async () => {
    setIsLoading(true);
    try {
      console.log('🧠 测试机器学习预测器...');
      
      // 模拟收集一些数据
      await dataCollector.collectUserData(testTarget, 150, {
        province: '北京',
        city: '北京',
        isp: '中国电信',
        networkType: '4G',
        deviceType: 'desktop',
        userAgent: navigator.userAgent,
        ipAddress: '127.0.0.1'
      });
      
      // 进行预测
      const prediction = latencyPredictor.predictLatency(
        testTarget,
        '北京',
        '北京',
        '中国电信',
        '4G',
        new Date().getHours()
      );
      
      // 获取模型统计
      const modelStats = latencyPredictor.getModelStats();
      
      setTestResults(prev => ({
        ...prev,
        mlPredictor: {
          success: true,
          data: {
            prediction,
            modelStats
          },
          timestamp: new Date().toISOString()
        }
      }));
      
      console.log('✅ ML预测完成:', { prediction, modelStats });
    } catch (error) {
      console.error('❌ ML预测失败:', error);
      setTestResults(prev => ({
        ...prev,
        mlPredictor: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // 运行所有测试
  const runAllTests = async () => {
    await testAdvancedNetworkTester();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testWebRTCLatency();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testMultiCloudTester();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testMLPredictor();
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          🧪 Ping测试改进功能测试页面
        </h1>
        
        <div className="mb-8">
          <label className="block text-sm font-medium mb-2">测试目标:</label>
          <input
            type="text"
            value={testTarget}
            onChange={(e) => setTestTarget(e.target.value)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white"
            placeholder="输入要测试的URL"
          />
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <button
            onClick={testAdvancedNetworkTester}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-md transition-colors"
          >
            🎯 高精度测试
          </button>
          
          <button
            onClick={testWebRTCLatency}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-md transition-colors"
          >
            🔗 WebRTC测试
          </button>
          
          <button
            onClick={testMultiCloudTester}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-md transition-colors"
          >
            ☁️ 多云测试
          </button>
          
          <button
            onClick={testMLPredictor}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 rounded-md transition-colors"
          >
            🧠 ML预测
          </button>
        </div>

        <div className="mb-8">
          <button
            onClick={runAllTests}
            disabled={isLoading}
            className="w-full px-6 py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 rounded-md transition-colors font-semibold"
          >
            {isLoading ? '🔄 测试中...' : '🚀 运行所有测试'}
          </button>
        </div>

        <div className="space-y-6">
          {Object.entries(testResults).map(([testName, result]: [string, any]) => (
            <div key={testName} className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                {result.success ? '✅' : '❌'} {testName}
                <span className="ml-2 text-sm text-gray-400">
                  {new Date(result.timestamp).toLocaleTimeString()}
                </span>
              </h3>
              
              {result.success ? (
                <pre className="bg-gray-900 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              ) : (
                <div className="text-red-400 bg-red-900/20 p-4 rounded">
                  错误: {result.error}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
