import { NextRequest, NextResponse } from 'next/server';

// 多云节点配置
const CLOUD_NODES = {
  aliyun: [
    {
      name: '北京',
      region: 'cn-beijing',
      endpoint: 'https://ping-function-beijing.cn-beijing.fc.aliyuncs.com',
      provider: 'aliyun'
    },
    {
      name: '上海',
      region: 'cn-shanghai', 
      endpoint: 'https://ping-function-shanghai.cn-shanghai.fc.aliyuncs.com',
      provider: 'aliyun'
    },
    {
      name: '深圳',
      region: 'cn-shenzhen',
      endpoint: 'https://ping-function-shenzhen.cn-shenzhen.fc.aliyuncs.com',
      provider: 'aliyun'
    }
  ],
  tencent: [
    {
      name: '北京',
      region: 'ap-beijing',
      endpoint: 'https://service-xxx.ap-beijing.apigateway.myqcloud.com/release/ping',
      provider: 'tencent'
    },
    {
      name: '上海',
      region: 'ap-shanghai',
      endpoint: 'https://service-xxx.ap-shanghai.apigateway.myqcloud.com/release/ping',
      provider: 'tencent'
    },
    {
      name: '广州',
      region: 'ap-guangzhou',
      endpoint: 'https://service-xxx.ap-guangzhou.apigateway.myqcloud.com/release/ping',
      provider: 'tencent'
    },
    {
      name: '成都',
      region: 'ap-chengdu',
      endpoint: 'https://service-xxx.ap-chengdu.apigateway.myqcloud.com/release/ping',
      provider: 'tencent'
    }
  ]
};

// 调用云函数进行真实ping测试
async function callCloudFunction(node: any, target: string): Promise<any> {
  try {
    const response = await fetch(node.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ target }),
      timeout: 10000
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const result = await response.json();
    return {
      name: node.name,
      region: node.region,
      provider: node.provider,
      latency: result.latency,
      success: result.success,
      timestamp: result.timestamp
    };
  } catch (error) {
    return {
      name: node.name,
      region: node.region,
      provider: node.provider,
      latency: 5000,
      success: false,
      error: error.message
    };
  }
}

// UptimeRobot API备用测试
async function getUptimeRobotData(target: string) {
  const apiKey = process.env.NEXT_PUBLIC_UPTIMEROBOT_API_KEY;
  if (!apiKey) return null;

  try {
    const response = await fetch('https://api.uptimerobot.com/v2/getMonitors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `api_key=${apiKey}&format=json&logs=1&logs_limit=1&url=${encodeURIComponent(target)}`
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('UptimeRobot API error:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { target } = await request.json();

    if (!target) {
      return NextResponse.json(
        { error: 'Target URL is required' },
        { status: 400 }
      );
    }

    console.log(`开始多云ping测试: ${target}`);

    // 并发调用所有云节点
    const allNodes = [...CLOUD_NODES.aliyun, ...CLOUD_NODES.tencent];
    const promises = allNodes.map(node => callCloudFunction(node, target));
    
    // 同时获取UptimeRobot数据作为参考
    const uptimeRobotPromise = getUptimeRobotData(target);

    // 等待所有测试完成
    const [results, uptimeRobotData] = await Promise.all([
      Promise.allSettled(promises),
      uptimeRobotPromise
    ]);

    // 处理结果
    const pingResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name: allNodes[index].name,
          region: allNodes[index].region,
          provider: allNodes[index].provider,
          latency: 5000,
          success: false,
          error: result.reason?.message || 'Unknown error'
        };
      }
    });

    // 按地区分组结果
    const groupedResults = pingResults.reduce((acc, result) => {
      if (!acc[result.name]) {
        acc[result.name] = [];
      }
      acc[result.name].push(result);
      return acc;
    }, {});

    // 计算每个地区的最佳延迟
    const finalResults = Object.keys(groupedResults).map(cityName => {
      const cityResults = groupedResults[cityName];
      const successResults = cityResults.filter(r => r.success);
      
      if (successResults.length > 0) {
        // 取最低延迟
        const bestResult = successResults.reduce((min, current) => 
          current.latency < min.latency ? current : min
        );
        
        return {
          name: cityName,
          latency: bestResult.latency,
          success: true,
          provider: bestResult.provider,
          region: bestResult.region
        };
      } else {
        return {
          name: cityName,
          latency: 5000,
          success: false,
          provider: cityResults[0]?.provider || 'unknown',
          region: cityResults[0]?.region || 'unknown'
        };
      }
    });

    return NextResponse.json({
      success: true,
      target,
      timestamp: new Date().toISOString(),
      results: finalResults,
      uptimeRobotData: uptimeRobotData ? {
        available: true,
        monitors: uptimeRobotData.monitors?.length || 0
      } : { available: false },
      totalNodes: allNodes.length,
      successfulNodes: pingResults.filter(r => r.success).length
    });

  } catch (error) {
    console.error('多云ping测试错误:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Multi-cloud ping API',
    availableNodes: {
      aliyun: CLOUD_NODES.aliyun.length,
      tencent: CLOUD_NODES.tencent.length,
      total: CLOUD_NODES.aliyun.length + CLOUD_NODES.tencent.length
    }
  });
}
