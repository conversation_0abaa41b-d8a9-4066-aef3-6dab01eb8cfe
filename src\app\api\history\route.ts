import { NextRequest, NextResponse } from 'next/server';

// 模拟数据库存储（在实际应用中应该使用真实数据库）
interface HistoryRecord {
  id: string;
  target: string;
  timestamp: number;
  latency: number;
  jitter: number;
  packetLoss: number;
  bandwidth: number;
  downloadSpeed: number;
  uploadSpeed: number;
  mtu: number;
  status: 'success' | 'failed' | 'timeout';
  testMethod: string;
  reliability: number;
  location?: {
    city: string;
    province: string;
    region: string;
  };
}

interface TrendData {
  timestamps: number[];
  latencies: number[];
  jitters: number[];
  packetLosses: number[];
  bandwidths: number[];
  averageLatency: number;
  averageJitter: number;
  averagePacketLoss: number;
  averageBandwidth: number;
  trend: 'improving' | 'degrading' | 'stable';
  reliability: number;
}

// 内存存储（生产环境应使用数据库）
const historyStorage = new Map<string, HistoryRecord[]>();

// GET - 获取历史数据
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const target = searchParams.get('target');
    const hours = parseInt(searchParams.get('hours') || '24');
    const type = searchParams.get('type') || 'records'; // records, trend, stats

    if (!target) {
      return NextResponse.json({ error: 'Target parameter is required' }, { status: 400 });
    }

    const records = getRecordsByTarget(target, hours);

    switch (type) {
      case 'trend':
        const trendData = calculateTrendData(records);
        return NextResponse.json({
          success: true,
          data: trendData,
          metadata: {
            target,
            hours,
            recordCount: records.length,
            type: 'trend'
          }
        });

      case 'stats':
        const stats = calculateStats(records);
        return NextResponse.json({
          success: true,
          data: stats,
          metadata: {
            target,
            hours,
            recordCount: records.length,
            type: 'stats'
          }
        });

      default:
        return NextResponse.json({
          success: true,
          data: records,
          metadata: {
            target,
            hours,
            recordCount: records.length,
            type: 'records'
          }
        });
    }

  } catch (error) {
    console.error('获取历史数据失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST - 保存历史记录
export async function POST(request: NextRequest) {
  try {
    const record: Omit<HistoryRecord, 'id'> = await request.json();

    if (!record.target || !record.timestamp) {
      return NextResponse.json({ error: 'Target and timestamp are required' }, { status: 400 });
    }

    // 生成ID
    const id = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullRecord: HistoryRecord = { ...record, id };

    // 保存记录
    const targetKey = record.target.toLowerCase();
    if (!historyStorage.has(targetKey)) {
      historyStorage.set(targetKey, []);
    }

    const records = historyStorage.get(targetKey)!;
    records.push(fullRecord);

    // 保持记录数量在合理范围内（最多1000条）
    if (records.length > 1000) {
      records.splice(0, records.length - 1000);
    }

    // 按时间戳排序
    records.sort((a, b) => b.timestamp - a.timestamp);

    return NextResponse.json({
      success: true,
      data: { id },
      metadata: {
        target: record.target,
        totalRecords: records.length
      }
    });

  } catch (error) {
    console.error('保存历史记录失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save history record',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE - 清除历史数据
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const target = searchParams.get('target');
    const days = parseInt(searchParams.get('days') || '30');

    if (target) {
      // 清除特定目标的数据
      const targetKey = target.toLowerCase();
      if (historyStorage.has(targetKey)) {
        const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
        const records = historyStorage.get(targetKey)!;
        const filteredRecords = records.filter(record => record.timestamp > cutoffTime);
        historyStorage.set(targetKey, filteredRecords);

        return NextResponse.json({
          success: true,
          data: { deletedCount: records.length - filteredRecords.length },
          metadata: { target, days }
        });
      }
    } else {
      // 清除所有过期数据
      const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
      let totalDeleted = 0;

      for (const [key, records] of historyStorage.entries()) {
        const originalCount = records.length;
        const filteredRecords = records.filter(record => record.timestamp > cutoffTime);
        historyStorage.set(key, filteredRecords);
        totalDeleted += originalCount - filteredRecords.length;
      }

      return NextResponse.json({
        success: true,
        data: { deletedCount: totalDeleted },
        metadata: { days }
      });
    }

    return NextResponse.json({
      success: true,
      data: { deletedCount: 0 },
      metadata: { target, days }
    });

  } catch (error) {
    console.error('清除历史数据失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete history data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 辅助函数
function getRecordsByTarget(target: string, hours: number): HistoryRecord[] {
  const targetKey = target.toLowerCase();
  const records = historyStorage.get(targetKey) || [];
  const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
  
  return records
    .filter(record => record.timestamp >= cutoffTime)
    .sort((a, b) => a.timestamp - b.timestamp);
}

function calculateTrendData(records: HistoryRecord[]): TrendData {
  const successfulRecords = records.filter(record => record.status === 'success');

  if (successfulRecords.length === 0) {
    return {
      timestamps: [],
      latencies: [],
      jitters: [],
      packetLosses: [],
      bandwidths: [],
      averageLatency: 0,
      averageJitter: 0,
      averagePacketLoss: 0,
      averageBandwidth: 0,
      trend: 'stable',
      reliability: 0
    };
  }

  const timestamps = successfulRecords.map(r => r.timestamp);
  const latencies = successfulRecords.map(r => r.latency);
  const jitters = successfulRecords.map(r => r.jitter);
  const packetLosses = successfulRecords.map(r => r.packetLoss);
  const bandwidths = successfulRecords.map(r => r.bandwidth);

  // 计算平均值
  const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
  const averageJitter = jitters.reduce((a, b) => a + b, 0) / jitters.length;
  const averagePacketLoss = packetLosses.reduce((a, b) => a + b, 0) / packetLosses.length;
  const averageBandwidth = bandwidths.reduce((a, b) => a + b, 0) / bandwidths.length;

  // 计算趋势
  const trend = calculateTrend(latencies);
  
  // 计算可靠性
  const reliability = successfulRecords.reduce((sum, r) => sum + r.reliability, 0) / successfulRecords.length;

  return {
    timestamps,
    latencies,
    jitters,
    packetLosses,
    bandwidths,
    averageLatency: Math.round(averageLatency),
    averageJitter: Math.round(averageJitter),
    averagePacketLoss: Math.round(averagePacketLoss * 100) / 100,
    averageBandwidth: Math.round(averageBandwidth),
    trend,
    reliability: Math.round(reliability)
  };
}

function calculateTrend(values: number[]): 'improving' | 'degrading' | 'stable' {
  if (values.length < 2) return 'stable';

  const mid = Math.floor(values.length / 2);
  const firstHalf = values.slice(0, mid);
  const secondHalf = values.slice(mid);

  const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

  const change = ((secondAvg - firstAvg) / firstAvg) * 100;

  if (change > 10) return 'degrading';  // 延迟增加10%以上
  if (change < -10) return 'improving'; // 延迟减少10%以上
  return 'stable';
}

function calculateStats(records: HistoryRecord[]) {
  const successfulRecords = records.filter(r => r.status === 'success');
  const latencies = successfulRecords.map(r => r.latency);

  if (latencies.length === 0) {
    return {
      uptime: 0,
      avgLatency: 0,
      maxLatency: 0,
      minLatency: 0,
      totalTests: records.length,
      successRate: 0,
      lastTestTime: 0
    };
  }

  return {
    uptime: (successfulRecords.length / records.length) * 100,
    avgLatency: latencies.reduce((a, b) => a + b, 0) / latencies.length,
    maxLatency: Math.max(...latencies),
    minLatency: Math.min(...latencies),
    totalTests: records.length,
    successRate: (successfulRecords.length / records.length) * 100,
    lastTestTime: Math.max(...records.map(r => r.timestamp))
  };
}
