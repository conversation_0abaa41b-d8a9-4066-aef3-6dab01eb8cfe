// 阿里云函数计算 - 真实网络延迟测试
const http = require('http');
const https = require('https');
const { URL } = require('url');

// 真实的TCP连接测试
function tcpPing(hostname, port, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const socket = new (require('net')).Socket();
    
    const timer = setTimeout(() => {
      socket.destroy();
      reject(new Error('Timeout'));
    }, timeout);
    
    socket.connect(port, hostname, () => {
      clearTimeout(timer);
      const latency = Date.now() - startTime;
      socket.destroy();
      resolve(latency);
    });
    
    socket.on('error', (err) => {
      clearTimeout(timer);
      socket.destroy();
      reject(err);
    });
  });
}

// HTTP请求延迟测试
function httpPing(url, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'HEAD',
      timeout: timeout,
      headers: {
        'User-Agent': 'AliyunFC-PingTester/1.0'
      }
    };
    
    const req = client.request(options, (res) => {
      const latency = Date.now() - startTime;
      resolve({
        latency,
        statusCode: res.statusCode,
        success: res.statusCode < 400
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// 多种方法综合测试
async function comprehensivePing(target) {
  const results = [];
  const urlObj = new URL(target);
  const hostname = urlObj.hostname;
  const port = urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80);
  
  // 1. TCP连接测试
  try {
    const tcpLatency = await tcpPing(hostname, port, 3000);
    results.push({
      method: 'TCP',
      latency: tcpLatency,
      success: true
    });
  } catch (error) {
    results.push({
      method: 'TCP',
      latency: 5000,
      success: false,
      error: error.message
    });
  }
  
  // 2. HTTP请求测试
  try {
    const httpResult = await httpPing(target, 3000);
    results.push({
      method: 'HTTP',
      latency: httpResult.latency,
      success: httpResult.success,
      statusCode: httpResult.statusCode
    });
  } catch (error) {
    results.push({
      method: 'HTTP',
      latency: 5000,
      success: false,
      error: error.message
    });
  }
  
  // 计算平均延迟
  const successfulResults = results.filter(r => r.success);
  const avgLatency = successfulResults.length > 0 
    ? Math.round(successfulResults.reduce((sum, r) => sum + r.latency, 0) / successfulResults.length)
    : 5000;
  
  return {
    target,
    latency: avgLatency,
    success: successfulResults.length > 0,
    details: results,
    timestamp: new Date().toISOString(),
    testCount: results.length,
    successCount: successfulResults.length
  };
}

// 阿里云函数计算入口
exports.handler = async (req, resp, context) => {
  console.log('🌐 阿里云函数开始执行ping测试');

  try {
    // 处理OPTIONS请求
    if (req.method === 'OPTIONS') {
      resp.send('');
      return;
    }

    // 解析请求体
    let body;
    if (req.body) {
      if (typeof req.body === 'string') {
        body = JSON.parse(req.body);
      } else if (Buffer.isBuffer(req.body)) {
        body = JSON.parse(req.body.toString());
      } else {
        body = req.body;
      }
    } else {
      body = req.queries || {};
    }

    console.log('请求参数:', body);

    const { target } = body;

    if (!target) {
      const errorResponse = {
        success: false,
        error: 'Target URL is required',
        provider: 'aliyun',
        region: process.env.APP_REGION || 'unknown',
        city: process.env.CITY || 'unknown'
      };

      resp.send(JSON.stringify(errorResponse));
      return;
    }

    // 执行综合ping测试
    const result = await comprehensivePing(target);

    // 添加阿里云特定信息
    const response = {
      ...result,
      provider: 'aliyun',
      region: process.env.APP_REGION || context.region || 'unknown',
      city: process.env.CITY || 'unknown',
      province: process.env.PROVINCE || 'unknown',
      functionName: context.function.name,
      requestId: context.requestId,
      testMethod: 'AliyunFC-RealPing'
    };

    console.log('✅ 测试完成:', response);

    resp.send(JSON.stringify(response));

  } catch (error) {
    console.error('❌ 测试失败:', error);

    const errorResponse = {
      success: false,
      error: error.message,
      provider: 'aliyun',
      region: process.env.APP_REGION || 'unknown',
      city: process.env.CITY || 'unknown',
      latency: 5000,
      timestamp: new Date().toISOString(),
      testMethod: 'AliyunFC-RealPing'
    };

    resp.send(JSON.stringify(errorResponse));
  }
};
