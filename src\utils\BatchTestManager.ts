// 分批测试管理器 - 优化并发处理和响应速度

export interface TestNode {
  name: string;
  province: string;
  priority: number;
  region: string;
  endpoints: string[];
}

export interface BatchConfig {
  batchSize: number;
  batchDelay: number; // 批次间延迟
  maxConcurrency: number; // 最大并发数
  timeout: number;
  retryAttempts: number;
  priorityFirst: boolean; // 是否优先测试高优先级节点
}

export interface BatchResult<T> {
  batchId: number;
  results: T[];
  errors: Error[];
  duration: number;
  successRate: number;
}

export interface ProgressCallback {
  (progress: number, stage: string, completedBatches: number, totalBatches: number): void;
}

class BatchTestManager {
  private defaultConfig: BatchConfig = {
    batchSize: 8, // 增加批次大小
    batchDelay: 200, // 减少批次间延迟
    maxConcurrency: 6, // 增加并发数
    timeout: 3000, // 减少超时时间
    retryAttempts: 1, // 减少重试次数
    priorityFirst: true
  };

  // 分批执行测试
  async executeBatchTests<T>(
    nodes: TestNode[],
    testFunction: (node: TestNode) => Promise<T>,
    config: Partial<BatchConfig> = {},
    onProgress?: ProgressCallback
  ): Promise<T[]> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    // 排序节点：优先级高的先测试
    const sortedNodes = finalConfig.priorityFirst 
      ? this.sortNodesByPriority(nodes)
      : nodes;

    // 分批
    const batches = this.createBatches(sortedNodes, finalConfig.batchSize);
    const allResults: T[] = [];
    
    console.log(`开始分批测试: ${batches.length} 批次，每批 ${finalConfig.batchSize} 个节点`);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchStartTime = performance.now();
      
      // 更新进度
      if (onProgress) {
        const progress = (i / batches.length) * 100;
        onProgress(progress, `测试第 ${i + 1} 批次`, i, batches.length);
      }

      try {
        // 执行当前批次
        const batchResults = await this.executeSingleBatch(
          batch,
          testFunction,
          finalConfig
        );
        
        allResults.push(...batchResults.results);
        
        const batchDuration = performance.now() - batchStartTime;
        console.log(`批次 ${i + 1} 完成: ${batchResults.results.length}/${batch.length} 成功, 耗时 ${Math.round(batchDuration)}ms`);
        
        // 批次间延迟（除了最后一批）
        if (i < batches.length - 1 && finalConfig.batchDelay > 0) {
          await this.delay(finalConfig.batchDelay);
        }
        
      } catch (error) {
        console.error(`批次 ${i + 1} 执行失败:`, error);
      }
    }

    // 最终进度更新
    if (onProgress) {
      onProgress(100, '测试完成', batches.length, batches.length);
    }

    console.log(`分批测试完成: 总共 ${allResults.length}/${nodes.length} 个节点成功`);
    return allResults;
  }

  // 执行单个批次
  private async executeSingleBatch<T>(
    nodes: TestNode[],
    testFunction: (node: TestNode) => Promise<T>,
    config: BatchConfig
  ): Promise<BatchResult<T>> {
    const batchStartTime = performance.now();
    const results: T[] = [];
    const errors: Error[] = [];

    // 使用信号量控制并发
    const semaphore = new Semaphore(config.maxConcurrency);
    
    const promises = nodes.map(async (node) => {
      await semaphore.acquire();
      
      try {
        const result = await this.executeWithRetry(
          () => testFunction(node),
          config.retryAttempts,
          config.timeout
        );
        results.push(result);
      } catch (error) {
        errors.push(error instanceof Error ? error : new Error(String(error)));
        console.warn(`节点 ${node.name} 测试失败:`, error);
      } finally {
        semaphore.release();
      }
    });

    await Promise.allSettled(promises);
    
    const duration = performance.now() - batchStartTime;
    const successRate = (results.length / nodes.length) * 100;

    return {
      batchId: Date.now(),
      results,
      errors,
      duration,
      successRate
    };
  }

  // 带重试的执行
  private async executeWithRetry<T>(
    fn: () => Promise<T>,
    maxAttempts: number,
    timeout: number
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await this.withTimeout(fn(), timeout);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < maxAttempts) {
          // 指数退避
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await this.delay(delay);
          console.log(`重试第 ${attempt} 次，延迟 ${delay}ms`);
        }
      }
    }

    throw lastError!;
  }

  // 超时包装器
  private withTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('操作超时')), timeout);
      })
    ]);
  }

  // 按优先级排序节点
  private sortNodesByPriority(nodes: TestNode[]): TestNode[] {
    return [...nodes].sort((a, b) => {
      // 优先级高的在前
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // 优先级相同时按名称排序
      return a.name.localeCompare(b.name);
    });
  }

  // 创建批次
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 智能批次大小计算
  calculateOptimalBatchSize(totalNodes: number, targetDuration: number = 10000): number {
    // 基于目标完成时间计算最优批次大小
    const estimatedTimePerNode = 2000; // 估算每个节点2秒
    const maxConcurrency = this.defaultConfig.maxConcurrency;
    
    const optimalBatchSize = Math.ceil(
      (targetDuration * maxConcurrency) / estimatedTimePerNode
    );
    
    // 限制在合理范围内
    return Math.max(3, Math.min(optimalBatchSize, 12));
  }

  // 动态调整配置
  adaptiveConfig(nodes: TestNode[], targetResponseTime: number = 15000): BatchConfig {
    const nodeCount = nodes.length;
    const config = { ...this.defaultConfig };

    if (nodeCount <= 10) {
      // 少量节点：小批次，低延迟
      config.batchSize = 3;
      config.batchDelay = 200;
      config.maxConcurrency = 3;
    } else if (nodeCount <= 20) {
      // 中等数量：平衡配置
      config.batchSize = 5;
      config.batchDelay = 300;
      config.maxConcurrency = 4;
    } else {
      // 大量节点：大批次，优化吞吐量
      config.batchSize = this.calculateOptimalBatchSize(nodeCount, targetResponseTime);
      config.batchDelay = 500;
      config.maxConcurrency = 6;
    }

    return config;
  }
}

// 信号量实现
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      this.waitQueue.push(resolve);
    });
  }

  release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!;
      this.permits--;
      resolve();
    }
  }
}

// 性能监控器
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getAverageMetric(name: string): number {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) return 0;
    return values.reduce((a, b) => a + b, 0) / values.length;
  }

  getMetricSummary(name: string): { avg: number, min: number, max: number, count: number } {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0 };
    }

    return {
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length
    };
  }

  clearMetrics(): void {
    this.metrics.clear();
  }
}

// 单例实例
export const batchTestManager = new BatchTestManager();
export const performanceMonitor = new PerformanceMonitor();

export default BatchTestManager;
