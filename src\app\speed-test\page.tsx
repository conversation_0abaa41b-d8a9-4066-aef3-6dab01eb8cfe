'use client';

import React, { useState } from 'react';

export default function SpeedTestPage() {
  const [target, setTarget] = useState('www.baidu.com');
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [fastMode, setFastMode] = useState(true);

  const runSpeedTest = async () => {
    if (!target.trim() || isRunning) return;

    setIsRunning(true);
    setResults(null);
    
    const startTime = Date.now();

    try {
      const response = await fetch('/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: target.trim(),
          maxNodes: fastMode ? 10 : 25,
          fastMode: fastMode,
          useBatchTesting: true
        })
      });

      const data = await response.json();
      const totalTime = Date.now() - startTime;

      setResults({
        ...data,
        totalTime,
        fastMode
      });

    } catch (error) {
      console.error('测试失败:', error);
      setResults({
        error: '测试失败',
        totalTime: Date.now() - startTime
      });
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-6">
            ⚡ Ping工具速度测试
          </h1>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                测试目标
              </label>
              <input
                type="text"
                value={target}
                onChange={(e) => setTarget(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入网址，如 www.baidu.com"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="fastMode"
                checked={fastMode}
                onChange={(e) => setFastMode(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="fastMode" className="text-sm text-gray-700">
                ⚡ 快速模式 (减少超时时间和节点数量)
              </label>
            </div>

            <button
              onClick={runSpeedTest}
              disabled={isRunning || !target.trim()}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isRunning || !target.trim()
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isRunning ? '测试中...' : '开始速度测试'}
            </button>
          </div>

          {results && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                测试结果
              </h3>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="bg-white p-3 rounded-lg">
                  <div className="text-sm text-gray-600">总耗时</div>
                  <div className="text-xl font-bold text-blue-600">
                    {results.totalTime}ms
                  </div>
                </div>
                <div className="bg-white p-3 rounded-lg">
                  <div className="text-sm text-gray-600">测试模式</div>
                  <div className="text-xl font-bold text-green-600">
                    {results.fastMode ? '⚡ 快速' : '🔍 完整'}
                  </div>
                </div>
              </div>

              {results.results && (
                <div className="bg-white p-3 rounded-lg">
                  <div className="text-sm text-gray-600 mb-2">
                    成功节点: {results.results.filter((r: any) => r.status === 'success').length} / {results.results.length}
                  </div>
                  <div className="max-h-40 overflow-y-auto">
                    {results.results.slice(0, 10).map((result: any, index: number) => (
                      <div key={index} className="flex justify-between items-center py-1 border-b border-gray-100 last:border-b-0">
                        <span className="text-sm text-gray-700">{result.node}</span>
                        <span className={`text-sm font-medium ${
                          result.status === 'success' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {result.status === 'success' ? `${result.ping}ms` : '超时'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {results.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="text-red-800">{results.error}</div>
                </div>
              )}
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-semibold text-blue-800 mb-2">💡 优化说明</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>快速模式</strong>: 超时时间从10秒减少到3秒，节点数量减少50%</li>
              <li>• <strong>WebRTC测试</strong>: 从30秒减少到8秒，测试次数从10次减少到3次</li>
              <li>• <strong>批量处理</strong>: 增加并发数，减少批次间延迟</li>
              <li>• <strong>客户端测试</strong>: 图片加载超时从8秒减少到2秒</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
