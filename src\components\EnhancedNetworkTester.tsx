'use client';

import React, { useState, useCallback } from 'react';

export interface EnhancedTestResult {
  latency: number;
  jitter: number;
  packetLoss: number;
  bandwidth: number;
  downloadSpeed: number;
  uploadSpeed: number;
  mtu: number;
  status: 'success' | 'failed' | 'timeout';
  testMethod: string;
  timestamp: number;
  reliability: number; // 0-100的可靠性评分
}

interface EnhancedNetworkTesterProps {
  target: string;
  onResult: (result: EnhancedTestResult) => void;
  onProgress: (progress: number, stage: string) => void;
}

export const EnhancedNetworkTester: React.FC<EnhancedNetworkTesterProps> = ({
  target,
  onResult,
  onProgress
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [internalProgress, setInternalProgress] = useState(0);
  const [internalStage, setInternalStage] = useState('');

  // 多方法延迟测试
  const performMultiMethodLatencyTest = useCallback(async (): Promise<{
    httpLatency: number[];
    wsLatency: number[];
    fetchLatency: number[];
    imageLatency: number[];
  }> => {
    const results = {
      httpLatency: [] as number[],
      wsLatency: [] as number[],
      fetchLatency: [] as number[],
      imageLatency: [] as number[]
    };

    // 确保URL格式正确
    let testUrl = target;
    if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
      testUrl = 'https://' + testUrl;
    }

    updateProgress(10, 'HTTP HEAD测试');
    
    // 方法1: HTTP HEAD请求测试 (多次测试取平均)
    for (let i = 0; i < 5; i++) {
      try {
        const start = performance.now();
        const controller = new AbortController();
        setTimeout(() => controller.abort(), 5000);

        await fetch(testUrl, {
          method: 'HEAD',
          mode: 'no-cors',
          signal: controller.signal,
          cache: 'no-cache'
        });

        const latency = performance.now() - start;
        results.httpLatency.push(latency);
      } catch (error) {
        }
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    updateProgress(30, 'WebSocket测试');

    // 方法2: WebSocket连接测试（只使用安全连接）
    const domain = new URL(testUrl).hostname;
    const wsConfigs = [
      { url: `wss://${domain}`, name: 'WSS安全连接' }
      // 移除不安全的ws://连接，避免Mixed Content错误
    ];

    for (const config of wsConfigs) {
      try {
        const start = performance.now();
        const ws = new WebSocket(config.url);

        const latency = await new Promise<number>((resolve, reject) => {
          const timeout = setTimeout(() => {
            if (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN) {
              ws.close();
            }
            reject(new Error('WebSocket超时'));
          }, 2000); // 减少超时时间

          ws.onopen = () => {
            clearTimeout(timeout);
            const latency = performance.now() - start;
            ws.close();
            resolve(latency);
          };

          ws.onerror = () => {
            clearTimeout(timeout);
            if (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN) {
              ws.close();
            }
            reject(new Error('WebSocket连接失败'));
          };
        });

        results.wsLatency.push(latency);
        break; // 成功一个就够了
      } catch (error) {
        }
    }

    updateProgress(50, 'Fetch API测试');

    // 方法3: Fetch API测试
    for (let i = 0; i < 3; i++) {
      try {
        const start = performance.now();
        const controller = new AbortController();
        setTimeout(() => controller.abort(), 5000);

        await fetch(testUrl, {
          method: 'GET',
          mode: 'no-cors',
          signal: controller.signal,
          cache: 'no-cache'
        });

        const latency = performance.now() - start;
        results.fetchLatency.push(latency);
      } catch (error) {
        }
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    updateProgress(70, '图片加载测试');

    // 方法4: 图片加载测试
    for (let i = 0; i < 3; i++) {
      try {
        const start = performance.now();
        const img = new Image();
        
        const latency = await new Promise<number>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('图片加载超时'));
          }, 5000);

          img.onload = () => {
            clearTimeout(timeout);
            const latency = performance.now() - start;
            resolve(latency);
          };

          img.onerror = () => {
            clearTimeout(timeout);
            reject(new Error('图片加载失败'));
          };

          // 尝试加载favicon或小图片
          img.src = `${testUrl}/favicon.ico?t=${Date.now()}&r=${Math.random()}`;
        });

        results.imageLatency.push(latency);
      } catch (error) {
        }
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return results;
  }, [target]);

  // 带宽测试
  const performBandwidthTest = useCallback(async (): Promise<{
    downloadSpeed: number;
    uploadSpeed: number;
  }> => {
    updateProgress(80, '带宽测试');

    // 简化的带宽测试
    const testSizes = [1024, 10240, 102400]; // 1KB, 10KB, 100KB
    let downloadSpeed = 0;
    let uploadSpeed = 0;

    try {
      // 下载速度测试
      for (const size of testSizes) {
        const start = performance.now();
        const testData = 'x'.repeat(size);
        const blob = new Blob([testData]);
        const url = URL.createObjectURL(blob);
        
        await fetch(url);
        const duration = (performance.now() - start) / 1000; // 秒
        const speed = (size * 8) / duration / 1000; // Kbps
        downloadSpeed = Math.max(downloadSpeed, speed);
        
        URL.revokeObjectURL(url);
      }

      // 上传速度测试（模拟）
      uploadSpeed = downloadSpeed * 0.3; // 通常上传速度是下载速度的30%

    } catch (error) {
      }

    return { downloadSpeed, uploadSpeed };
  }, []);

  // MTU发现
  const discoverMTU = useCallback(async (): Promise<number> => {
    // 简化的MTU发现，实际应该通过二分查找
    const commonMTUs = [1500, 1492, 1472, 1460, 1400, 1280, 1200];
    
    for (const mtu of commonMTUs) {
      try {
        // 尝试发送接近MTU大小的数据
        const testData = 'x'.repeat(mtu - 100); // 减去头部开销
        const response = await fetch(target, {
          method: 'POST',
          body: testData,
          mode: 'no-cors'
        });
        return mtu;
      } catch (error) {
        continue;
      }
    }
    
    return 1500; // 默认MTU
  }, [target]);

  // 计算统计指标
  const calculateStats = (latencies: number[]) => {
    if (latencies.length === 0) return { avg: 0, jitter: 0, loss: 100 };

    const avg = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    
    // 计算抖动
    let jitterSum = 0;
    for (let i = 1; i < latencies.length; i++) {
      jitterSum += Math.abs(latencies[i] - latencies[i - 1]);
    }
    const jitter = latencies.length > 1 ? jitterSum / (latencies.length - 1) : 0;

    return { avg, jitter, loss: 0 };
  };

  // 更新进度的辅助函数
  const updateProgress = (progress: number, stage: string) => {
    setInternalProgress(progress);
    setInternalStage(stage);
    onProgress(progress, stage);
  };

  // 执行完整的增强测试
  const runEnhancedTest = async () => {
    setIsRunning(true);
    setInternalProgress(0);
    setInternalStage('');

    try {
      updateProgress(0, '开始增强网络测试');

      // 1. 多方法延迟测试
      const latencyResults = await performMultiMethodLatencyTest();

      // 2. 带宽测试
      const bandwidthResults = await performBandwidthTest();

      // 3. MTU发现
      updateProgress(90, 'MTU发现');
      const mtu = await discoverMTU();

      // 4. 综合分析
      updateProgress(95, '分析结果');
      
      // 合并所有延迟数据
      const allLatencies = [
        ...latencyResults.httpLatency,
        ...latencyResults.wsLatency,
        ...latencyResults.fetchLatency,
        ...latencyResults.imageLatency
      ].filter(l => l > 0);

      const stats = calculateStats(allLatencies);
      
      // 计算可靠性评分
      const methodCount = [
        latencyResults.httpLatency.length > 0,
        latencyResults.wsLatency.length > 0,
        latencyResults.fetchLatency.length > 0,
        latencyResults.imageLatency.length > 0
      ].filter(Boolean).length;

      const reliability = Math.min(100, (methodCount / 4) * 100 + (allLatencies.length / 15) * 100);

      const result: EnhancedTestResult = {
        latency: Math.round(stats.avg),
        jitter: Math.round(stats.jitter),
        packetLoss: stats.loss,
        bandwidth: Math.round(bandwidthResults.downloadSpeed),
        downloadSpeed: Math.round(bandwidthResults.downloadSpeed),
        uploadSpeed: Math.round(bandwidthResults.uploadSpeed),
        mtu,
        status: allLatencies.length > 0 ? 'success' : 'failed',
        testMethod: `Enhanced Multi-Method (${methodCount}/4 methods)`,
        timestamp: Date.now(),
        reliability: Math.round(reliability)
      };

      updateProgress(100, '测试完成');
      onResult(result);

    } catch (error) {
      updateProgress(100, '测试失败');
      onResult({
        latency: 0,
        jitter: 0,
        packetLoss: 100,
        bandwidth: 0,
        downloadSpeed: 0,
        uploadSpeed: 0,
        mtu: 1500,
        status: 'failed',
        testMethod: 'Enhanced Test Failed',
        timestamp: Date.now(),
        reliability: 0
      });
    } finally {
      setIsRunning(false);
      // 延迟重置进度
      setTimeout(() => {
        setInternalProgress(0);
        setInternalStage('');
      }, 2000);
    }
  };

  return (
    <div className="enhanced-network-tester">
      <button
        onClick={runEnhancedTest}
        disabled={isRunning}
        className={`relative px-4 py-2 rounded-lg font-medium transition-colors duration-300 overflow-hidden ${
          isRunning
            ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
            : 'bg-purple-600 hover:bg-purple-700 text-white'
        }`}
      >
        {/* 内置进度条 */}
        {isRunning && (
          <div
            className="absolute inset-0 bg-purple-500 opacity-30 transition-all duration-300"
            style={{ width: `${internalProgress}%` }}
          />
        )}

        {/* 按钮文本 */}
        <span className="relative z-10">
          {isRunning ? (
            <span className="flex items-center space-x-2">
              <span className="animate-spin">🔄</span>
              <span className="text-xs">
                {internalStage} ({Math.round(internalProgress)}%)
              </span>
            </span>
          ) : (
            '⚡ 增强网络测试'
          )}
        </span>
      </button>
    </div>
  );
};

export default EnhancedNetworkTester;
